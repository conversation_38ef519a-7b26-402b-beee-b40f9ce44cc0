# KPI Result Storage System - Implementation Analysis

## Overview

This document provides a comprehensive analysis of three different approaches for storing KPI query results in ClickHouse, optimized for Parquet export in both pivot and horizontal facts formats.

## Problem Statement

The current system needs to:
- Store KPI query results efficiently in ClickHouse
- Support export to Parquet format
- Provide two export formats: pivot and horizontal facts
- Maintain metadata about jobs, queries, axes, filters, and periods
- Optimize for storage efficiency and export performance
- Handle large datasets with good performance

## Three Implementation Approaches

### Approach 1: Normalized Storage (RECOMMENDED)

**Architecture:**
- `results_metadata`: Job and query metadata
- `results_axes`: Dimension data (axis_name, position_name, position_number, axis_id)
- `results_facts`: Measure data (fact_name, fact_id, value, decimal_places)

**Pros:**
- ✅ **Flexible and extensible**: Easy to add new axes or facts without schema changes
- ✅ **Storage efficient**: No wasted space for sparse data
- ✅ **Query flexibility**: Can easily filter by specific axes or facts
- ✅ **Normalized design**: Follows database best practices
- ✅ **Easy maintenance**: Clear separation of concerns
- ✅ **Supports complex queries**: Can handle arbitrary axis combinations
- ✅ **Good for analytics**: Easy to aggregate and analyze data

**Cons:**
- ❌ **Join complexity**: Requires joins for full data reconstruction
- ❌ **Query performance**: May be slower for simple exports due to joins
- ❌ **More complex queries**: Export queries are more complex

**Best For:**
- Systems with varying axis structures
- Complex analytical requirements
- Long-term data storage
- Systems where flexibility is more important than raw speed

**Storage Estimate:** ~60-80 bytes per fact-axis combination

### Approach 2: Wide Table Storage

**Architecture:**
- Single `results_wide` table with fixed columns for up to 5 axes
- Pre-defined axis columns (axis1_name, axis1_position_number, etc.)
- One row per fact-axis combination

**Pros:**
- ✅ **Simple queries**: No joins required for export
- ✅ **Fast exports**: Direct SELECT statements
- ✅ **Easy to understand**: Single table structure
- ✅ **Good performance**: Optimized for read operations
- ✅ **Parquet friendly**: Structure maps well to columnar format

**Cons:**
- ❌ **Limited flexibility**: Fixed number of axes (5 in our implementation)
- ❌ **Storage waste**: Empty columns for results with fewer axes
- ❌ **Schema rigidity**: Difficult to change axis structure
- ❌ **Maintenance complexity**: Need to handle varying axis counts
- ❌ **Not scalable**: Cannot handle more than predefined axis count

**Best For:**
- Systems with consistent axis structure
- Performance-critical export operations
- Simple reporting requirements
- Fixed schema environments

**Storage Estimate:** ~120-150 bytes per row (due to empty columns)

### Approach 3: Hybrid Columnar Storage

**Architecture:**
- `results_metadata`: Same as Approach 1
- `results_columnar`: Arrays for axes and facts data
- Optimized for columnar operations

**Pros:**
- ✅ **Parquet optimized**: Arrays map perfectly to Parquet format
- ✅ **Compact storage**: Efficient array storage in ClickHouse
- ✅ **Fast exports**: Minimal transformation needed
- ✅ **Flexible**: Can handle varying numbers of axes/facts
- ✅ **Columnar friendly**: Designed for analytical workloads
- ✅ **Good compression**: Arrays compress well

**Cons:**
- ❌ **Complex queries**: Array operations are more complex
- ❌ **Limited filtering**: Harder to filter on specific axis values
- ❌ **Learning curve**: Requires understanding of array operations
- ❌ **Debugging difficulty**: Harder to inspect data manually
- ❌ **ClickHouse specific**: Less portable to other databases

**Best For:**
- Parquet-heavy workflows
- Read-mostly workloads
- Systems with good ClickHouse expertise
- Analytical applications

**Storage Estimate:** ~50-70 bytes per row (most efficient)

## Performance Comparison

| Metric | Normalized | Wide Table | Columnar |
|--------|------------|------------|----------|
| **Storage Efficiency** | Good (★★★☆) | Poor (★★☆☆) | Excellent (★★★★) |
| **Export Speed** | Moderate (★★★☆) | Fast (★★★★) | Fast (★★★★) |
| **Query Flexibility** | Excellent (★★★★) | Good (★★★☆) | Moderate (★★☆☆) |
| **Maintenance** | Easy (★★★★) | Moderate (★★★☆) | Hard (★★☆☆) |
| **Scalability** | Excellent (★★★★) | Poor (★★☆☆) | Good (★★★☆) |
| **Development Time** | Moderate | Fast | Slow |

## Export Format Support

### Pivot Format
All approaches support pivot format with different complexity levels:

**Normalized:** Requires complex JOIN and PIVOT operations
```sql
SELECT * FROM (
  SELECT axis_name, position_name, fact_name, value
  FROM results_axes ra
  JOIN results_facts rf USING (result_id, period_name)
) PIVOT (sum(value) FOR fact_name IN ('revenue', 'units'))
```

**Wide Table:** Simple SELECT with column mapping
```sql
SELECT axis1_position_name, axis2_position_name, fact_value
FROM results_wide
WHERE result_id = 'xxx'
```

**Columnar:** Array operations to reconstruct pivot
```sql
SELECT arrayJoin(axis_names), arrayJoin(fact_values)
FROM results_columnar
WHERE result_id = 'xxx'
```

### Horizontal Facts Format
**Normalized:** Natural fit - data is already in this format
**Wide Table:** Requires UNPIVOT operations
**Columnar:** Requires array expansion

## Recommendations

### Primary Recommendation: Normalized Storage (Approach 1)

**Why:**
1. **Future-proof**: Can handle any axis/fact combination
2. **Maintainable**: Clear, understandable structure
3. **Flexible**: Supports complex analytical queries
4. **Standard**: Follows database normalization principles
5. **Extensible**: Easy to add new features

**Implementation Priority:**
1. Start with Normalized approach for production
2. Implement Columnar approach for high-volume scenarios
3. Consider Wide Table for specific performance-critical use cases

### Performance Optimizations for Normalized Approach:

1. **Materialized Views**: Pre-compute common export formats
2. **Partitioning**: Partition by date and result_id
3. **Indexing**: Bloom filter indexes on result_id
4. **Compression**: Use ClickHouse compression for storage efficiency
5. **TTL**: Automatic cleanup of old results

## Implementation Roadmap

### Phase 1: Core Implementation (Week 1-2)
- [ ] Implement Normalized storage approach
- [ ] Basic Parquet export functionality
- [ ] Integration with existing JobService
- [ ] Unit tests and validation

### Phase 2: Optimization (Week 3)
- [ ] Performance tuning and indexing
- [ ] Materialized views for common queries
- [ ] Export format optimizations
- [ ] Load testing and benchmarking

### Phase 3: Advanced Features (Week 4)
- [ ] Columnar approach implementation
- [ ] Advanced export configurations
- [ ] Monitoring and alerting
- [ ] Documentation and training

### Phase 4: Production Deployment (Week 5)
- [ ] Production deployment
- [ ] Migration from existing system
- [ ] Performance monitoring
- [ ] User feedback and iterations

## Risk Mitigation

### Data Integrity
- Implement comprehensive validation
- Use transactions where possible
- Regular data consistency checks
- Backup and recovery procedures

### Performance
- Implement query timeouts
- Monitor resource usage
- Implement query optimization
- Plan for scaling

### Maintenance
- Automated cleanup procedures
- Monitoring and alerting
- Documentation and runbooks
- Team training

## Conclusion

The **Normalized Storage approach** provides the best balance of flexibility, maintainability, and performance for the KPI result storage system. While it may not be the fastest for simple exports, its advantages in terms of flexibility and future-proofing make it the recommended choice for production implementation.

The system can be enhanced with performance optimizations and potentially supplemented with the Columnar approach for specific high-volume use cases.
