# KPI Result Storage System

A comprehensive solution for storing KPI query results in ClickHouse with optimized Parquet export capabilities.

## 🎯 Overview

This system provides a robust, scalable solution for storing KPI query results directly in ClickHouse with support for efficient export to Parquet format in both pivot and horizontal facts formats. The implementation offers three different storage approaches to meet various performance and scalability requirements.

## 📋 Requirements Met

✅ **Results Metadata Table**: Complete job and query metadata storage  
✅ **Axes Data Storage**: Efficient dimension data storage with position information  
✅ **Facts Data Storage**: Optimized measure data with decimal precision  
✅ **Pivot Format Export**: Full support for pivot table exports  
✅ **Horizontal Facts Export**: Comprehensive horizontal format exports  
✅ **Three Implementation Approaches**: Normalized, Wide Table, and Columnar  
✅ **Performance Optimization**: Indexing, partitioning, and TTL strategies  
✅ **Comprehensive Testing**: Full test suite with multiple scenarios  

## 🏗️ Architecture

### Storage Approaches

#### 1. Normalized Storage (Recommended)
- **Tables**: `results_metadata`, `results_axes`, `results_facts`
- **Best For**: Flexible schemas, complex analytics, long-term storage
- **Pros**: Highly flexible, storage efficient, extensible
- **Cons**: Require<PERSON> joins for full data reconstruction

#### 2. Wide Table Storage
- **Tables**: `results_wide` (single table with fixed axis columns)
- **Best For**: Consistent schemas, fast exports, simple reporting
- **Pros**: No joins required, fast exports, simple structure
- **Cons**: Limited flexibility, storage waste for sparse data

#### 3. Columnar Storage
- **Tables**: `results_metadata`, `results_columnar` (array-based)
- **Best For**: Parquet-heavy workflows, analytical workloads
- **Pros**: Optimal for Parquet, compact storage, fast exports
- **Cons**: Complex queries, ClickHouse-specific features

## 📁 File Structure

```
├── sql/
│   └── clickhouse_schemas.sql          # ClickHouse table definitions
├── src/
│   ├── core/
│   │   └── result_store.py             # Main ResultStore implementation
│   ├── models/
│   │   └── result_models.py            # Pydantic data models
│   ├── services/
│   │   └── kpi_result_service.py       # High-level result management
│   └── exporters/
│       └── parquet_exporter.py         # Parquet export functionality
├── tests/
│   └── test_kpi_result_storage.py      # Comprehensive test suite
├── examples/
│   └── kpi_result_usage_examples.py    # Usage examples and demos
└── docs/
    └── kpi_result_storage_analysis.md  # Detailed technical analysis
```

## 🚀 Quick Start

### 1. Database Setup

```sql
-- Execute the schema file to create tables
SOURCE sql/clickhouse_schemas.sql;
```

### 2. Basic Usage

```python
from src.core.result_store import ResultStore, StorageConfig
from src.models.result_models import StorageApproach

# Initialize with normalized storage (recommended)
config = StorageConfig(approach=StorageApproach.NORMALIZED)
result_store = ResultStore(connection, config)

# The existing JobService will automatically use the new storage
# No changes needed to existing job processing code
```

### 3. Export to Parquet

```python
from src.exporters.parquet_exporter import ParquetExporter
from src.models.result_models import ExportRequest, ExportFormat

exporter = ParquetExporter(connection)

# Export in horizontal facts format
request = ExportRequest(
    result_ids=['result_123'],
    export_format=ExportFormat.HORIZONTAL_FACTS,
    output_path='exports/kpi_results.parquet'
)

result = exporter.export_results(request)
```

## 📊 Performance Characteristics

| Metric | Normalized | Wide Table | Columnar |
|--------|------------|------------|----------|
| **Storage Efficiency** | ★★★☆ | ★★☆☆ | ★★★★ |
| **Export Speed** | ★★★☆ | ★★★★ | ★★★★ |
| **Query Flexibility** | ★★★★ | ★★★☆ | ★★☆☆ |
| **Maintenance** | ★★★★ | ★★★☆ | ★★☆☆ |
| **Scalability** | ★★★★ | ★★☆☆ | ★★★☆ |

## 🔧 Configuration Options

### Storage Configuration

```python
from src.core.result_store import StorageConfig
from src.models.result_models import StorageApproach

config = StorageConfig(
    approach=StorageApproach.NORMALIZED,  # Storage approach
    default_retention_days=30,            # Data retention period
    enable_compression=True,              # Enable ClickHouse compression
    batch_size=10000,                     # Batch size for inserts
    enable_ttl=True                       # Enable automatic cleanup
)
```

### Export Configuration

```python
from src.models.result_models import HorizontalExportConfig

horizontal_config = HorizontalExportConfig(
    include_all_axes=True,                # Include all axis columns
    fact_name_column="metric_name",       # Custom fact name column
    fact_value_column="metric_value",     # Custom fact value column
    separate_periods=False,               # Single file vs separate files
    include_metadata_columns=True         # Include job metadata
)
```

## 📈 Export Formats

### Horizontal Facts Format
Each fact-axis combination as a separate row:
```
result_id | period | axis_name | position_name | fact_name | fact_value
result_1  | 2024-01| product   | Product A     | revenue   | 1000.0
result_1  | 2024-01| product   | Product A     | units     | 100
```

### Pivot Format
Axes as columns, facts as rows:
```
result_id | period | fact_name | Product A | Product B | Product C
result_1  | 2024-01| revenue   | 1000.0    | 1500.0    | 800.0
result_1  | 2024-01| units     | 100       | 150       | 80
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Run all tests
python -m pytest tests/test_kpi_result_storage.py -v

# Run specific test categories
python -m pytest tests/test_kpi_result_storage.py::TestResultStore -v
python -m pytest tests/test_kpi_result_storage.py::TestParquetExporter -v
```

## 📚 Examples

See `examples/kpi_result_usage_examples.py` for comprehensive usage examples including:

- Basic integration with existing JobService
- Export to both Parquet formats
- Different storage approach configurations
- Advanced querying and filtering
- Maintenance and cleanup operations

## 🔍 Monitoring and Maintenance

### Storage Statistics
```python
from src.services.kpi_result_service import KPIResultService

service = KPIResultService(connection)
stats = service.get_storage_statistics()
print(f"Total results: {stats.total_results}")
print(f"Storage size: {stats.total_size_bytes / 1024 / 1024:.2f} MB")
```

### Cleanup Operations
```python
# Cleanup expired results (automatic via TTL)
cleaned_count = service.cleanup_expired_results()

# Delete specific result
success = service.delete_result(result_id)
```

## 🎯 Recommendations

### For Production Use:
1. **Start with Normalized Storage** - Most flexible and maintainable
2. **Implement proper indexing** - Use bloom filter indexes for result_id
3. **Set up monitoring** - Track storage usage and export performance
4. **Configure TTL** - Automatic cleanup of old results
5. **Use compression** - Enable ClickHouse compression for storage efficiency

### For High-Volume Scenarios:
1. **Consider Columnar Storage** - Best for Parquet-heavy workflows
2. **Implement materialized views** - Pre-compute common export formats
3. **Use partitioning** - Partition by date and result_id
4. **Optimize batch sizes** - Tune batch_size based on data volume

## 🚨 Important Notes

### Integration with Existing Code
- The new `ResultStore` is designed to be a drop-in replacement
- Existing `JobService` code requires minimal changes
- The `store_result_direct` method maintains compatibility with current interfaces

### Data Migration
- The system can run alongside existing storage during transition
- Gradual migration is recommended for production systems
- Backup existing data before migration

### Performance Considerations
- Normalized approach may be slower for simple exports due to joins
- Wide table approach has storage overhead for sparse data
- Columnar approach requires ClickHouse expertise for optimization

## 📞 Support

For questions or issues:
1. Check the comprehensive test suite for usage patterns
2. Review the technical analysis document for detailed comparisons
3. Examine the usage examples for common scenarios
4. Refer to the ClickHouse documentation for database-specific features

## 🔄 Future Enhancements

Potential improvements for future versions:
- Automatic storage approach selection based on data characteristics
- Real-time export streaming for large datasets
- Integration with external data catalogs
- Advanced compression strategies
- Multi-tenant support with data isolation
