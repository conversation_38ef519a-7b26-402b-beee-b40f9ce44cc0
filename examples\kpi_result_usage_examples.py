"""
Usage examples for the KPI Result Storage System.

This file demonstrates how to use the new KPI result storage and export system
with practical examples for different scenarios.
"""

import os
from datetime import datetime, timedelta
from pathlib import Path

from src.core.result_store import ResultStore, StorageConfig
from src.services.kpi_result_service import KPIResultService
from src.exporters.parquet_exporter import ParquetExporter
from src.models.result_models import (
    StorageApproach, ExportFormat, ExportRequest,
    PivotExportConfig, HorizontalExportConfig
)
from src.core.connection_manager import connection_manager


def example_1_basic_usage():
    """
    Example 1: Basic usage with the existing JobService integration.
    
    This shows how the new system integrates with existing code.
    """
    print("=== Example 1: Basic Integration ===")
    
    # The existing JobService will automatically use the new ResultStore
    # No changes needed to existing job processing code
    
    # Get connection
    connection = connection_manager.get_clickhouse_connection()
    
    # Create result service for querying stored results
    result_service = KPIResultService(connection=connection)
    
    # List recent results
    recent_results = result_service.list_results(
        date_from=datetime.now() - timedelta(days=7),
        limit=10
    )
    
    print(f"Found {len(recent_results)} recent results")
    for result in recent_results:
        print(f"  - {result.result_id}: {result.analysis_name} ({result.total_rows} rows)")


def example_2_export_to_parquet():
    """
    Example 2: Export stored results to Parquet format.
    
    This demonstrates both pivot and horizontal export formats.
    """
    print("\n=== Example 2: Parquet Export ===")
    
    # Get connection and services
    connection = connection_manager.get_clickhouse_connection()
    result_service = KPIResultService(connection=connection)
    exporter = ParquetExporter(connection)
    
    # Find some results to export
    results = result_service.list_results(limit=5)
    if not results:
        print("No results found to export")
        return
    
    result_ids = [r.result_id for r in results[:2]]  # Export first 2 results
    
    # Create output directory
    output_dir = Path("exports")
    output_dir.mkdir(exist_ok=True)
    
    # Example 2a: Horizontal Facts Export
    print("\n--- Horizontal Facts Export ---")
    
    horizontal_request = ExportRequest(
        result_ids=result_ids,
        export_format=ExportFormat.HORIZONTAL_FACTS,
        output_path=str(output_dir / "kpi_results_horizontal.parquet"),
        compression="snappy"
    )
    
    horizontal_config = HorizontalExportConfig(
        include_all_axes=True,
        fact_name_column="metric_name",
        fact_value_column="metric_value",
        include_metadata_columns=True
    )
    
    result = exporter.export_results(horizontal_request, horizontal_config=horizontal_config)
    
    if result.success:
        print(f"✓ Exported {result.row_count} rows to {result.file_path}")
        print(f"  File size: {result.file_size_bytes / 1024 / 1024:.2f} MB")
        print(f"  Export time: {result.export_duration_ms:.2f} ms")
    else:
        print(f"✗ Export failed: {result.error_message}")
    
    # Example 2b: Pivot Format Export
    print("\n--- Pivot Format Export ---")
    
    pivot_request = ExportRequest(
        result_ids=result_ids,
        export_format=ExportFormat.PIVOT,
        output_path=str(output_dir / "kpi_results_pivot.parquet"),
        compression="snappy"
    )
    
    pivot_config = PivotExportConfig(
        axis_columns=["axis_name", "position_name"],
        fact_rows=["fact_name"],
        value_column="value",
        include_totals=False
    )
    
    result = exporter.export_results(pivot_request, pivot_config=pivot_config)
    
    if result.success:
        print(f"✓ Exported {result.row_count} rows to {result.file_path}")
        print(f"  File size: {result.file_size_bytes / 1024 / 1024:.2f} MB")
        print(f"  Export time: {result.export_duration_ms:.2f} ms")
    else:
        print(f"✗ Export failed: {result.error_message}")


def example_3_different_storage_approaches():
    """
    Example 3: Using different storage approaches.
    
    This shows how to configure different storage strategies.
    """
    print("\n=== Example 3: Storage Approaches ===")
    
    connection = connection_manager.get_clickhouse_connection()
    
    # Example 3a: Normalized Storage (Default)
    print("\n--- Normalized Storage ---")
    normalized_config = StorageConfig(
        approach=StorageApproach.NORMALIZED,
        default_retention_days=30,
        enable_compression=True
    )
    
    normalized_store = ResultStore(connection, normalized_config)
    print(f"✓ Initialized normalized storage with {normalized_config.approach.value} approach")
    
    # Example 3b: Wide Table Storage
    print("\n--- Wide Table Storage ---")
    wide_config = StorageConfig(
        approach=StorageApproach.WIDE_TABLE,
        default_retention_days=30,
        batch_size=5000
    )
    
    wide_store = ResultStore(connection, wide_config)
    print(f"✓ Initialized wide table storage with {wide_config.approach.value} approach")
    
    # Example 3c: Columnar Storage
    print("\n--- Columnar Storage ---")
    columnar_config = StorageConfig(
        approach=StorageApproach.COLUMNAR,
        default_retention_days=30,
        enable_compression=True
    )
    
    columnar_store = ResultStore(connection, columnar_config)
    print(f"✓ Initialized columnar storage with {columnar_config.approach.value} approach")


def example_4_advanced_querying():
    """
    Example 4: Advanced querying and filtering of stored results.
    
    This demonstrates the query capabilities of the result service.
    """
    print("\n=== Example 4: Advanced Querying ===")
    
    connection = connection_manager.get_clickhouse_connection()
    result_service = KPIResultService(connection=connection)
    
    # Example 4a: Filter by user and date range
    print("\n--- Filter by User and Date ---")
    user_results = result_service.list_results(
        username="specific_user",
        date_from=datetime.now() - timedelta(days=30),
        date_to=datetime.now(),
        limit=20
    )
    
    print(f"Found {len(user_results)} results for specific user in last 30 days")
    
    # Example 4b: Search by analysis name
    print("\n--- Search by Analysis Name ---")
    search_results = result_service.search_results("revenue analysis", limit=10)
    print(f"Found {len(search_results)} results matching 'revenue analysis'")
    
    # Example 4c: Get detailed information about a specific result
    if search_results:
        result_id = search_results[0].result_id
        print(f"\n--- Detailed Info for {result_id} ---")
        
        details = result_service.get_result_details(result_id)
        if details:
            print(f"Metadata: {details['metadata']['analysis_name']}")
            print(f"Axes: {len(details['axes'])} different axes")
            print(f"Facts: {len(details['facts'])} different facts")
            print(f"Storage approach: {details['storage_approach']}")
    
    # Example 4d: Get storage statistics
    print("\n--- Storage Statistics ---")
    stats = result_service.get_storage_statistics()
    print(f"Total results stored: {stats.total_results}")
    print(f"Storage size: {stats.total_size_bytes / 1024 / 1024:.2f} MB")
    if stats.oldest_result:
        print(f"Oldest result: {stats.oldest_result}")
    if stats.newest_result:
        print(f"Newest result: {stats.newest_result}")


def example_5_export_with_filters():
    """
    Example 5: Export with specific filters and configurations.
    
    This shows advanced export scenarios with filtering.
    """
    print("\n=== Example 5: Filtered Exports ===")
    
    connection = connection_manager.get_clickhouse_connection()
    result_service = KPIResultService(connection=connection)
    exporter = ParquetExporter(connection)
    
    # Find results to export
    results = result_service.list_results(limit=3)
    if not results:
        print("No results found for export")
        return
    
    result_ids = [r.result_id for r in results]
    output_dir = Path("exports/filtered")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Example 5a: Export specific periods only
    print("\n--- Export Specific Periods ---")
    
    period_request = ExportRequest(
        result_ids=result_ids,
        export_format=ExportFormat.HORIZONTAL_FACTS,
        output_path=str(output_dir / "specific_periods.parquet"),
        periods=["2024-01", "2024-02"],  # Only these periods
        compression="gzip"
    )
    
    result = exporter.export_results(period_request)
    if result.success:
        print(f"✓ Exported filtered periods: {result.row_count} rows")
    
    # Example 5b: Export specific facts only
    print("\n--- Export Specific Facts ---")
    
    facts_request = ExportRequest(
        result_ids=result_ids,
        export_format=ExportFormat.HORIZONTAL_FACTS,
        output_path=str(output_dir / "specific_facts.parquet"),
        facts=["revenue", "units"],  # Only these facts
        compression="snappy"
    )
    
    result = exporter.export_results(facts_request)
    if result.success:
        print(f"✓ Exported filtered facts: {result.row_count} rows")
    
    # Example 5c: Export with separate files per period
    print("\n--- Separate Files per Period ---")
    
    separate_config = HorizontalExportConfig(
        separate_periods=True,
        include_metadata_columns=True
    )
    
    separate_request = ExportRequest(
        result_ids=result_ids,
        export_format=ExportFormat.HORIZONTAL_FACTS,
        output_path=str(output_dir / "by_period.parquet"),
        compression="snappy"
    )
    
    result = exporter.export_results(separate_request, horizontal_config=separate_config)
    if result.success:
        print(f"✓ Exported to separate files: {result.row_count} total rows")
        if result.metadata and 'exported_files' in result.metadata:
            for file_path in result.metadata['exported_files']:
                print(f"  - {file_path}")


def example_6_maintenance_operations():
    """
    Example 6: Maintenance and cleanup operations.
    
    This demonstrates system maintenance capabilities.
    """
    print("\n=== Example 6: Maintenance Operations ===")
    
    connection = connection_manager.get_clickhouse_connection()
    result_service = KPIResultService(connection=connection)
    
    # Example 6a: Cleanup expired results
    print("\n--- Cleanup Expired Results ---")
    cleaned_count = result_service.cleanup_expired_results()
    print(f"Cleaned up {cleaned_count} expired results")
    
    # Example 6b: Delete specific result
    print("\n--- Delete Specific Result ---")
    results = result_service.list_results(limit=1)
    if results:
        result_id = results[0].result_id
        # In practice, you'd want to be more careful about deletion
        # success = result_service.delete_result(result_id)
        # print(f"Deletion {'successful' if success else 'failed'} for {result_id}")
        print(f"Would delete result: {result_id}")
    
    # Example 6c: Get export preview
    print("\n--- Export Preview ---")
    if results:
        result_id = results[0].result_id
        preview = result_service.get_export_preview(
            result_id, 
            ExportFormat.HORIZONTAL_FACTS, 
            limit=5
        )
        if preview:
            print(f"Preview format: {preview['format']}")
            print(f"Estimated rows: {preview['row_count_estimate']}")


def main():
    """Run all examples."""
    print("KPI Result Storage System - Usage Examples")
    print("=" * 50)
    
    try:
        example_1_basic_usage()
        example_2_export_to_parquet()
        example_3_different_storage_approaches()
        example_4_advanced_querying()
        example_5_export_with_filters()
        example_6_maintenance_operations()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"\nError running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
