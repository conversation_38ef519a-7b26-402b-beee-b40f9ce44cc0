"""
Data models for KPI result storage and export.

This module defines Pydantic models for storing and managing KPI query results
in ClickHouse, optimized for Parquet export in both pivot and horizontal formats.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from enum import Enum


class ExportFormat(str, Enum):
    """Supported export formats."""
    PIVOT = "pivot"
    HORIZONTAL_FACTS = "horizontal_facts"


class StorageApproach(str, Enum):
    """Different storage approaches for KPI results."""
    NORMALIZED = "normalized"      # Separate tables for metadata, axes, facts
    WIDE_TABLE = "wide_table"     # Single wide table with fixed columns
    COLUMNAR = "columnar"         # Array-based columnar storage


class ResultMetadata(BaseModel):
    """Metadata for a KPI result set."""
    job_id: str
    result_id: str
    analysis_name: str
    id_panel: str
    query_steps: str
    query_ids: List[str]
    username: str
    job_duration: str
    axes_info: str                # JSON string
    filters_info: str             # JSON string
    periods: List[str]
    facts_info: str               # JSON string
    retention_days: int = 30
    created_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None


class AxisData(BaseModel):
    """Axis (dimension) data for a result."""
    result_id: str
    period_name: str
    axis_name: str
    position_name: str
    position_number: int
    axis_id: str
    created_at: Optional[datetime] = None


class FactData(BaseModel):
    """Fact (measure) data for a result."""
    result_id: str
    period_name: str
    fact_name: str
    fact_id: str
    value: float
    decimal_places: int
    axis_keys: str                # JSON string with axis position numbers
    created_at: Optional[datetime] = None


class WideTableRow(BaseModel):
    """Row structure for wide table storage approach."""
    result_id: str
    job_id: str
    period_name: str
    analysis_name: str
    
    # Dynamic axis columns (up to 5 axes)
    axis1_name: str = ""
    axis1_position_name: str = ""
    axis1_position_number: int = 0
    axis2_name: str = ""
    axis2_position_name: str = ""
    axis2_position_number: int = 0
    axis3_name: str = ""
    axis3_position_name: str = ""
    axis3_position_number: int = 0
    axis4_name: str = ""
    axis4_position_name: str = ""
    axis4_position_number: int = 0
    axis5_name: str = ""
    axis5_position_name: str = ""
    axis5_position_number: int = 0
    
    # Fact data
    fact_name: str
    fact_value: float
    fact_decimal_places: int
    
    # Metadata
    username: str
    created_at: Optional[datetime] = None


class ColumnarRow(BaseModel):
    """Row structure for columnar storage approach."""
    result_id: str
    period_name: str
    
    # Arrays for efficient storage
    axis_names: List[str]
    axis_position_names: List[str]
    axis_position_numbers: List[int]
    axis_ids: List[str]
    
    # Facts as arrays
    fact_names: List[str]
    fact_values: List[float]
    fact_decimal_places: List[int]
    
    # Row identifier
    row_hash: str
    created_at: Optional[datetime] = None


class ExportRequest(BaseModel):
    """Request for exporting KPI results to Parquet."""
    result_ids: List[str]
    export_format: ExportFormat
    output_path: str
    include_metadata: bool = True
    compression: str = "snappy"
    
    # Optional filters
    periods: Optional[List[str]] = None
    facts: Optional[List[str]] = None
    axes: Optional[List[str]] = None


class ExportResult(BaseModel):
    """Result of a Parquet export operation."""
    success: bool
    file_path: Optional[str] = None
    row_count: int = 0
    file_size_bytes: int = 0
    export_duration_ms: float = 0
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class StorageStats(BaseModel):
    """Statistics about stored results."""
    total_results: int
    total_size_bytes: int
    oldest_result: Optional[datetime] = None
    newest_result: Optional[datetime] = None
    results_by_approach: Dict[StorageApproach, int] = Field(default_factory=dict)
    avg_export_time_ms: float = 0


class ResultSummary(BaseModel):
    """Summary information about a stored result."""
    result_id: str
    job_id: str
    analysis_name: str
    periods: List[str]
    total_rows: int
    axes_count: int
    facts_count: int
    storage_approach: StorageApproach
    created_at: datetime
    expires_at: Optional[datetime] = None
    file_size_estimate_mb: float = 0


class PivotExportConfig(BaseModel):
    """Configuration for pivot format export."""
    axis_columns: List[str]       # Which axes to use as columns
    fact_rows: List[str]          # Which facts to include as rows
    value_column: str = "value"   # Name for the value column
    include_totals: bool = False  # Whether to include total rows/columns
    null_value: Union[str, float, None] = None  # How to handle null values


class HorizontalExportConfig(BaseModel):
    """Configuration for horizontal facts format export."""
    include_all_axes: bool = True     # Include all axis columns
    fact_name_column: str = "fact"    # Column name for fact names
    fact_value_column: str = "value"  # Column name for fact values
    separate_periods: bool = False    # Create separate files per period
    include_metadata_columns: bool = True  # Include job metadata columns
