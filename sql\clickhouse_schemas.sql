-- ClickHouse Schema for KPI Results Storage
-- Optimized for Parquet export with three different implementation approaches

-- =============================================================================
-- APPROACH 1: NORMALIZED STORAGE (Recommended for flexibility)
-- =============================================================================

-- Results Metadata Table
CREATE TABLE IF NOT EXISTS results_metadata (
    job_id String,
    result_id String,
    analysis_name String,
    id_panel String,
    query_steps String,
    query_ids Array(String),
    username String,
    job_duration String,
    axes_info String,           -- JSON string with axes configuration
    filters_info String,        -- JSON string with filters configuration  
    periods Array(String),      -- Array of period names
    facts_info String,          -- JSON string with facts configuration
    retention_days Int32,
    created_at DateTime DEFAULT now(),
    expires_at DateTime MATERIALIZED addDays(created_at, retention_days)
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(created_at)
ORDER BY (job_id, result_id, created_at)
TTL expires_at DELETE
SETTINGS index_granularity = 8192;

-- Axes Data Table (Dimensions)
CREATE TABLE IF NOT EXISTS results_axes (
    result_id String,
    period_name String,
    axis_name String,
    position_name String,
    position_number UInt32,
    axis_id String,
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(created_at)
ORDER BY (result_id, period_name, axis_name, position_number)
SETTINGS index_granularity = 8192;

-- Facts Data Table (Measures)
CREATE TABLE IF NOT EXISTS results_facts (
    result_id String,
    period_name String,
    fact_name String,
    fact_id String,
    value Float64,
    decimal_places UInt8,
    -- Dimension keys for joining with axes
    axis_keys String,           -- JSON string with axis position numbers
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(created_at)
ORDER BY (result_id, period_name, fact_name, axis_keys)
SETTINGS index_granularity = 8192;

-- =============================================================================
-- APPROACH 2: WIDE TABLE STORAGE (Optimized for analytical queries)
-- =============================================================================

-- Single wide table with all data
CREATE TABLE IF NOT EXISTS results_wide (
    result_id String,
    job_id String,
    period_name String,
    analysis_name String,
    
    -- Dynamic axis columns (up to 10 axes supported)
    axis1_name String DEFAULT '',
    axis1_position_name String DEFAULT '',
    axis1_position_number UInt32 DEFAULT 0,
    axis2_name String DEFAULT '',
    axis2_position_name String DEFAULT '',
    axis2_position_number UInt32 DEFAULT 0,
    axis3_name String DEFAULT '',
    axis3_position_name String DEFAULT '',
    axis3_position_number UInt32 DEFAULT 0,
    axis4_name String DEFAULT '',
    axis4_position_name String DEFAULT '',
    axis4_position_number UInt32 DEFAULT 0,
    axis5_name String DEFAULT '',
    axis5_position_name String DEFAULT '',
    axis5_position_number UInt32 DEFAULT 0,
    
    -- Fact columns
    fact_name String,
    fact_value Float64,
    fact_decimal_places UInt8,
    
    -- Metadata
    username String,
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
PARTITION BY (toYYYYMM(created_at), result_id)
ORDER BY (result_id, period_name, fact_name, axis1_position_number, axis2_position_number)
SETTINGS index_granularity = 8192;

-- =============================================================================
-- APPROACH 3: HYBRID COLUMNAR STORAGE (Best for Parquet export)
-- =============================================================================

-- Metadata table (same as Approach 1)
-- Uses the results_metadata table defined above

-- Columnar facts table optimized for Parquet export
CREATE TABLE IF NOT EXISTS results_columnar (
    result_id String,
    period_name String,
    
    -- Axes as arrays for efficient storage
    axis_names Array(String),
    axis_position_names Array(String), 
    axis_position_numbers Array(UInt32),
    axis_ids Array(String),
    
    -- Facts as arrays
    fact_names Array(String),
    fact_values Array(Float64),
    fact_decimal_places Array(UInt8),
    
    -- Row identifier for pivot operations
    row_hash String,
    
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(created_at)
ORDER BY (result_id, period_name, row_hash)
SETTINGS index_granularity = 8192;

-- =============================================================================
-- INDEXES AND MATERIALIZED VIEWS
-- =============================================================================

-- Index for fast job lookups
CREATE INDEX IF NOT EXISTS idx_job_id ON results_metadata (job_id) TYPE bloom_filter GRANULARITY 1;

-- Index for fast result lookups
CREATE INDEX IF NOT EXISTS idx_result_id ON results_axes (result_id) TYPE bloom_filter GRANULARITY 1;
CREATE INDEX IF NOT EXISTS idx_result_id_facts ON results_facts (result_id) TYPE bloom_filter GRANULARITY 1;

-- Materialized view for pivot format preparation
CREATE MATERIALIZED VIEW IF NOT EXISTS results_pivot_prep
ENGINE = AggregatingMergeTree()
PARTITION BY toYYYYMM(created_at)
ORDER BY (result_id, period_name)
AS SELECT
    result_id,
    period_name,
    groupArray((axis_name, position_name, position_number)) as axes_data,
    groupArray((fact_name, value, decimal_places)) as facts_data,
    created_at
FROM results_axes ra
LEFT JOIN results_facts rf USING (result_id, period_name)
GROUP BY result_id, period_name, created_at;

-- =============================================================================
-- CLEANUP AND MAINTENANCE
-- =============================================================================

-- Cleanup old results based on retention policy
CREATE TABLE IF NOT EXISTS cleanup_log (
    cleanup_date Date,
    table_name String,
    rows_deleted UInt64,
    cleanup_duration_ms UInt64
) ENGINE = Log;
