"""
ClickHouse result storage system for KPI query results.

This module provides a comprehensive system for storing KPI query results in ClickHouse
with support for multiple storage approaches and optimized Parquet export capabilities.
"""

import json
import logging
import hashlib
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass

from src.core.connection import ClickHouseConnection
from src.models.result_models import (
    ResultMetadata, AxisData, FactData, WideTableRow, ColumnarRow,
    StorageApproach, ExportFormat, StorageStats, ResultSummary
)
from src.models.axis import Period
from src.models.kpi import KPIType


@dataclass
class StorageConfig:
    """Configuration for result storage."""
    approach: StorageApproach = StorageApproach.COLUMNAR
    default_retention_days: int = 30
    enable_compression: bool = True
    batch_size: int = 10000
    enable_ttl: bool = True


class ResultStore:
    """
    Main class for storing and retrieving KPI results in ClickHouse.
    
    Supports three different storage approaches:
    1. Normalized: Separate tables for metadata, axes, and facts
    2. Wide Table: Single table with fixed columns for axes
    3. Columnar: Array-based storage optimized for Parquet export
    """
    
    def __init__(
        self, 
        connection: ClickHouseConnection,
        config: Optional[StorageConfig] = None
    ):
        """
        Initialize the ResultStore.
        
        Args:
            connection: ClickHouse database connection
            config: Storage configuration options
        """
        self.connection = connection
        self.config = config or StorageConfig()
        self.logger = logging.getLogger(__name__)
        
        # Initialize database tables
        self._initialize_tables()
    
    def _initialize_tables(self) -> None:
        """Initialize ClickHouse tables for result storage."""
        try:
            # Read and execute schema from SQL file
            schema_path = "sql/clickhouse_schemas.sql"
            try:
                with open(schema_path, 'r') as f:
                    schema_sql = f.read()
                
                # Split by semicolon and execute each statement
                statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
                for statement in statements:
                    if statement and not statement.startswith('--'):
                        self.connection.execute_command(statement)
                        
                self.logger.info("Successfully initialized ClickHouse tables")
                
            except FileNotFoundError:
                self.logger.warning(f"Schema file {schema_path} not found, creating basic tables")
                self._create_basic_tables()
                
        except Exception as e:
            self.logger.error(f"Failed to initialize tables: {e}")
            raise
    
    def _create_basic_tables(self) -> None:
        """Create basic tables if schema file is not available."""
        # Create basic normalized tables
        metadata_sql = """
        CREATE TABLE IF NOT EXISTS results_metadata (
            job_id String,
            result_id String,
            analysis_name String,
            id_panel String,
            query_steps String,
            query_ids Array(String),
            username String,
            job_duration String,
            axes_info String,
            filters_info String,
            periods Array(String),
            facts_info String,
            retention_days Int32,
            created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (job_id, result_id, created_at)
        """
        
        axes_sql = """
        CREATE TABLE IF NOT EXISTS results_axes (
            result_id String,
            period_name String,
            axis_name String,
            position_name String,
            position_number UInt32,
            axis_id String,
            created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (result_id, period_name, axis_name, position_number)
        """
        
        facts_sql = """
        CREATE TABLE IF NOT EXISTS results_facts (
            result_id String,
            period_name String,
            fact_name String,
            fact_id String,
            value Float64,
            decimal_places UInt8,
            axis_keys String,
            created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (result_id, period_name, fact_name, axis_keys)
        """
        
        for sql in [metadata_sql, axes_sql, facts_sql]:
            self.connection.execute_command(sql)
    
    def store_result_direct(
        self,
        query_text: str,
        job_id: str,
        analysis_name: str,
        period: Union[Period, List[Period]],
        kpi_type: KPIType,
        id_panel: int,
        axes: Dict[str, Any],
        filters: Dict[str, Any],
        combined_result_id: Optional[str] = None,
        query_steps: str = "",
        query_ids: Optional[List[str]] = None,
        settings: Optional[Dict[str, Any]] = None,
        username: str = "",
        su_fact_name: Optional[str] = None,
        job_duration: float = 0,
        error_info: Optional[Dict[str, Any]] = None,
        retention_days: int = 30
    ) -> str:
        """
        Store KPI query results directly in ClickHouse.
        
        This method maintains compatibility with the existing codebase while
        providing enhanced storage capabilities.
        
        Args:
            query_text: The KPI query that was executed
            job_id: Unique job identifier
            analysis_name: Name of the analysis
            period: Period(s) for the analysis
            kpi_type: Type of KPI analysis
            id_panel: Panel identifier
            axes: Axes configuration
            filters: Filters configuration
            combined_result_id: Optional combined result ID
            query_steps: Complete query steps
            query_ids: List of query IDs
            settings: Query execution settings
            username: User who executed the job
            su_fact_name: SU fact name if applicable
            job_duration: Job duration in milliseconds
            error_info: Error information if job failed
            retention_days: Number of days to retain results
            
        Returns:
            Result ID for the stored data
        """
        try:
            # Generate result ID if not provided
            result_id = combined_result_id or self._generate_result_id(job_id, analysis_name)
            
            # Convert period to list if single period
            periods = period if isinstance(period, list) else [period]
            
            # Execute the query to get results
            if not error_info:
                query_results = self._execute_and_parse_query(query_text, settings)
            else:
                query_results = None
            
            # Store based on configured approach
            if self.config.approach == StorageApproach.NORMALIZED:
                self._store_normalized(
                    result_id=result_id,
                    job_id=job_id,
                    analysis_name=analysis_name,
                    periods=periods,
                    axes=axes,
                    filters=filters,
                    query_steps=query_steps,
                    query_ids=query_ids or [],
                    username=username,
                    job_duration=job_duration,
                    su_fact_name=su_fact_name,
                    retention_days=retention_days,
                    query_results=query_results,
                    error_info=error_info
                )
            elif self.config.approach == StorageApproach.WIDE_TABLE:
                self._store_wide_table(result_id, query_results, job_id, analysis_name, periods, username)
            elif self.config.approach == StorageApproach.COLUMNAR:
                self._store_columnar(result_id, query_results, periods)
            
            self.logger.info(f"Successfully stored result {result_id} using {self.config.approach.value} approach")
            return result_id
            
        except Exception as e:
            self.logger.error(f"Failed to store result: {e}")
            raise
    
    def _generate_result_id(self, job_id: str, analysis_name: str) -> str:
        """Generate a unique result ID."""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        hash_input = f"{job_id}_{analysis_name}_{timestamp}"
        hash_part = hashlib.md5(hash_input.encode()).hexdigest()[:8]
        return f"result_{job_id}_{hash_part}"
    
    def _execute_and_parse_query(
        self, 
        query_text: str, 
        settings: Optional[Dict[str, Any]] = None
    ) -> Optional[pd.DataFrame]:
        """Execute query and return results as DataFrame."""
        try:
            if query_text.strip().startswith("SELECT 'Error'"):
                return None  # Skip error queries
                
            result = self.connection.get_query_dataframe(query_text, settings=settings)
            return result
            
        except Exception as e:
            self.logger.warning(f"Failed to execute query for result parsing: {e}")
            return None

    def _store_normalized(
        self,
        result_id: str,
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        axes: Dict[str, Any],
        filters: Dict[str, Any],
        query_steps: str,
        query_ids: List[str],
        username: str,
        job_duration: float,
        su_fact_name: Optional[str],
        retention_days: int,
        query_results: Optional[pd.DataFrame],
        error_info: Optional[Dict[str, Any]]
    ) -> None:
        """Store results using normalized approach (separate tables)."""

        # 1. Store metadata
        metadata = ResultMetadata(
            job_id=job_id,
            result_id=result_id,
            analysis_name=analysis_name,
            id_panel=str(1),  # Default panel
            query_steps=query_steps,
            query_ids=query_ids,
            username=username,
            job_duration=self._format_duration(job_duration),
            axes_info=json.dumps(axes),
            filters_info=json.dumps(filters),
            periods=[p.label for p in periods],
            facts_info=json.dumps({"su_fact_name": su_fact_name}) if su_fact_name else "{}",
            retention_days=retention_days
        )

        self._insert_metadata(metadata)

        # 2. Store axes and facts data if query was successful
        if query_results is not None and not query_results.empty:
            self._store_axes_and_facts_normalized(result_id, periods, query_results)

        # 3. Store error info if present
        if error_info:
            self._store_error_info(result_id, error_info)

    def _store_wide_table(
        self,
        result_id: str,
        query_results: Optional[pd.DataFrame],
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        username: str
    ) -> None:
        """Store results using wide table approach."""
        if query_results is None or query_results.empty:
            return

        # Transform results to wide table format
        wide_rows = self._transform_to_wide_format(
            query_results, result_id, job_id, analysis_name, periods, username
        )

        # Insert into wide table
        self._insert_wide_table_rows(wide_rows)

    def _store_columnar(
        self,
        result_id: str,
        query_results: Optional[pd.DataFrame],
        periods: List[Period]
    ) -> None:
        """Store results using columnar approach."""
        if query_results is None or query_results.empty:
            return

        # Transform results to columnar format
        columnar_rows = self._transform_to_columnar_format(query_results, result_id, periods)

        # Insert into columnar table
        self._insert_columnar_rows(columnar_rows)

    def _format_duration(self, duration_ms: float) -> str:
        """Format duration in milliseconds to human-readable string."""
        if duration_ms < 1000:
            return f"{duration_ms:.0f}ms"
        elif duration_ms < 60000:
            return f"{duration_ms/1000:.1f}s"
        else:
            minutes = int(duration_ms / 60000)
            seconds = (duration_ms % 60000) / 1000
            return f"{minutes}m {seconds:.1f}s"

    def _insert_metadata(self, metadata: ResultMetadata) -> None:
        """Insert metadata into ClickHouse."""
        insert_sql = """
        INSERT INTO results_metadata (
            job_id, result_id, analysis_name, id_panel, query_steps, query_ids,
            username, job_duration, axes_info, filters_info, periods, facts_info,
            retention_days, created_at
        ) VALUES
        """

        values = (
            metadata.job_id,
            metadata.result_id,
            metadata.analysis_name,
            metadata.id_panel,
            metadata.query_steps,
            metadata.query_ids,
            metadata.username,
            metadata.job_duration,
            metadata.axes_info,
            metadata.filters_info,
            metadata.periods,
            metadata.facts_info,
            metadata.retention_days,
            datetime.now()
        )

        self.connection.client.insert('results_metadata', [values])

    def _store_axes_and_facts_normalized(
        self,
        result_id: str,
        periods: List[Period],
        query_results: pd.DataFrame
    ) -> None:
        """Extract and store axes and facts data from query results."""

        # Extract dimension information
        dimension_info = self._extract_dimension_info(query_results)

        # Extract fact columns
        fact_columns = self._extract_fact_columns(query_results, dimension_info)

        # Process each row of results
        axes_data = []
        facts_data = []

        for _, row in query_results.iterrows():
            # Determine period (could be from column or inferred)
            period_name = self._get_period_from_row(row, periods)

            # Extract axes data
            axis_keys = {}
            for dim_name, dim_info in dimension_info.items():
                position_col = dim_info['position_col']
                name_col = dim_info['name_col']

                if position_col in row and name_col in row:
                    position_num = int(row[position_col])
                    position_name = str(row[name_col])

                    axes_data.append(AxisData(
                        result_id=result_id,
                        period_name=period_name,
                        axis_name=dim_name,
                        position_name=position_name,
                        position_number=position_num,
                        axis_id=f"{dim_name}_{position_num}"
                    ))

                    axis_keys[dim_name] = position_num

            # Extract facts data
            axis_keys_json = json.dumps(axis_keys)

            for fact_col in fact_columns:
                if fact_col in row and pd.notna(row[fact_col]):
                    # Determine decimal places (simple heuristic)
                    value = float(row[fact_col])
                    decimal_places = self._get_decimal_places(value)

                    facts_data.append(FactData(
                        result_id=result_id,
                        period_name=period_name,
                        fact_name=fact_col,
                        fact_id=f"fact_{fact_col}",
                        value=value,
                        decimal_places=decimal_places,
                        axis_keys=axis_keys_json
                    ))

        # Insert axes and facts data
        if axes_data:
            self._insert_axes_data(axes_data)
        if facts_data:
            self._insert_facts_data(facts_data)

    def _extract_dimension_info(self, df: pd.DataFrame) -> Dict[str, Dict[str, str]]:
        """Extract dimension information from DataFrame columns."""
        dimensions = {}

        # Look for position_number and name columns
        for col in df.columns:
            if col.endswith('_position_number'):
                dim_name = col.replace('_position_number', '')
                name_col = f"{dim_name}_name" if f"{dim_name}_name" in df.columns else f"{dim_name}"

                if name_col in df.columns:
                    dimensions[dim_name] = {
                        'position_col': col,
                        'name_col': name_col
                    }

        return dimensions

    def _extract_fact_columns(self, df: pd.DataFrame, dimension_info: Dict[str, Dict[str, str]]) -> List[str]:
        """Extract fact columns from DataFrame."""
        # Get all dimension-related columns
        dim_columns = set()
        for dim_info in dimension_info.values():
            dim_columns.add(dim_info['position_col'])
            dim_columns.add(dim_info['name_col'])

        # Add other non-fact columns
        non_fact_columns = dim_columns | {'period_name', 'Fact'}

        # Return columns that are not dimensions or metadata
        fact_columns = []
        for col in df.columns:
            if col not in non_fact_columns and df[col].dtype in ['float64', 'int64', 'float32', 'int32']:
                fact_columns.append(col)

        return fact_columns

    def _get_period_from_row(self, row: pd.Series, periods: List[Period]) -> str:
        """Get period name from row data."""
        if 'period_name' in row:
            return str(row['period_name'])
        elif len(periods) == 1:
            return periods[0].label
        else:
            return periods[0].label  # Default to first period

    def _get_decimal_places(self, value: float) -> int:
        """Determine number of decimal places for a value."""
        if value == int(value):
            return 0

        # Convert to string and count decimal places
        str_val = f"{value:.10f}".rstrip('0')
        if '.' in str_val:
            return len(str_val.split('.')[1])
        return 0

    def _insert_axes_data(self, axes_data: List[AxisData]) -> None:
        """Insert axes data into ClickHouse."""
        if not axes_data:
            return

        rows = []
        for axis in axes_data:
            rows.append((
                axis.result_id,
                axis.period_name,
                axis.axis_name,
                axis.position_name,
                axis.position_number,
                axis.axis_id,
                datetime.now()
            ))

        self.connection.client.insert('results_axes', rows)

    def _insert_facts_data(self, facts_data: List[FactData]) -> None:
        """Insert facts data into ClickHouse."""
        if not facts_data:
            return

        rows = []
        for fact in facts_data:
            rows.append((
                fact.result_id,
                fact.period_name,
                fact.fact_name,
                fact.fact_id,
                fact.value,
                fact.decimal_places,
                fact.axis_keys,
                datetime.now()
            ))

        self.connection.client.insert('results_facts', rows)

    def _store_error_info(self, result_id: str, error_info: Dict[str, Any]) -> None:
        """Store error information."""
        # For now, we'll store error info as a special fact
        error_fact = FactData(
            result_id=result_id,
            period_name="error",
            fact_name="error_message",
            fact_id="error",
            value=0.0,
            decimal_places=0,
            axis_keys=json.dumps(error_info)
        )

        self._insert_facts_data([error_fact])

    def _transform_to_wide_format(
        self,
        df: pd.DataFrame,
        result_id: str,
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        username: str
    ) -> List[WideTableRow]:
        """Transform DataFrame to wide table format."""
        # This is a simplified implementation
        # In practice, you'd need more sophisticated logic to handle dynamic axes
        rows = []

        for _, row in df.iterrows():
            wide_row = WideTableRow(
                result_id=result_id,
                job_id=job_id,
                period_name=self._get_period_from_row(row, periods),
                analysis_name=analysis_name,
                fact_name="default_fact",
                fact_value=0.0,
                fact_decimal_places=0,
                username=username
            )
            rows.append(wide_row)

        return rows

    def _insert_wide_table_rows(self, rows: List[WideTableRow]) -> None:
        """Insert wide table rows into ClickHouse."""
        # Implementation would depend on the specific wide table schema
        pass

    def _transform_to_columnar_format(
        self,
        df: pd.DataFrame,
        result_id: str,
        periods: List[Period]
    ) -> List[ColumnarRow]:
        """Transform DataFrame to columnar format."""
        # Simplified implementation
        rows = []

        for _, row in df.iterrows():
            columnar_row = ColumnarRow(
                result_id=result_id,
                period_name=self._get_period_from_row(row, periods),
                axis_names=[],
                axis_position_names=[],
                axis_position_numbers=[],
                axis_ids=[],
                fact_names=[],
                fact_values=[],
                fact_decimal_places=[],
                row_hash=hashlib.md5(str(row.to_dict()).encode()).hexdigest()
            )
            rows.append(columnar_row)

        return rows

    def _insert_columnar_rows(self, rows: List[ColumnarRow]) -> None:
        """Insert columnar rows into ClickHouse."""
        # Implementation would depend on the specific columnar table schema
        pass

    # =============================================================================
    # COMPATIBILITY METHODS (for existing codebase)
    # =============================================================================

    def create_result_table_for_all_periods(self, result_id: str) -> Dict[str, Any]:
        """
        Compatibility method for existing codebase.

        Returns information about the stored result.
        """
        try:
            summary = self.get_result_summary(result_id)
            return {
                'created': True,
                'row_count': summary.total_rows if summary else 0,
                'result_id': result_id
            }
        except Exception as e:
            self.logger.error(f"Failed to get result summary for {result_id}: {e}")
            return {
                'created': False,
                'row_count': 0,
                'error': str(e)
            }

    def get_result_summary(self, result_id: str) -> Optional[ResultSummary]:
        """Get summary information about a stored result."""
        try:
            # Query metadata
            metadata_query = """
            SELECT * FROM results_metadata
            WHERE result_id = %(result_id)s
            LIMIT 1
            """

            metadata_result = self.connection.get_query_result(
                metadata_query,
                settings={'result_id': result_id}
            )

            if not metadata_result or len(metadata_result.data) == 0:
                return None

            metadata = metadata_result.data[0]

            # Count rows in axes and facts tables
            axes_count_query = """
            SELECT count() as cnt FROM results_axes
            WHERE result_id = %(result_id)s
            """

            facts_count_query = """
            SELECT count() as cnt FROM results_facts
            WHERE result_id = %(result_id)s
            """

            axes_count = self.connection.get_query_result(
                axes_count_query,
                settings={'result_id': result_id}
            ).data[0]['cnt']

            facts_count = self.connection.get_query_result(
                facts_count_query,
                settings={'result_id': result_id}
            ).data[0]['cnt']

            return ResultSummary(
                result_id=result_id,
                job_id=metadata['job_id'],
                analysis_name=metadata['analysis_name'],
                periods=metadata['periods'],
                total_rows=axes_count + facts_count,
                axes_count=axes_count,
                facts_count=facts_count,
                storage_approach=self.config.approach,
                created_at=metadata['created_at'],
                file_size_estimate_mb=self._estimate_file_size(axes_count + facts_count)
            )

        except Exception as e:
            self.logger.error(f"Failed to get result summary: {e}")
            return None

    def _estimate_file_size(self, row_count: int) -> float:
        """Estimate file size in MB based on row count."""
        # Rough estimate: 100 bytes per row on average
        return (row_count * 100) / (1024 * 1024)

    def get_storage_stats(self) -> StorageStats:
        """Get statistics about stored results."""
        try:
            stats_query = """
            SELECT
                count() as total_results,
                min(created_at) as oldest_result,
                max(created_at) as newest_result
            FROM results_metadata
            """

            result = self.connection.get_query_result(stats_query)
            if result and result.data:
                data = result.data[0]
                return StorageStats(
                    total_results=data['total_results'],
                    total_size_bytes=0,  # Would need more complex calculation
                    oldest_result=data['oldest_result'],
                    newest_result=data['newest_result'],
                    results_by_approach={self.config.approach: data['total_results']}
                )

            return StorageStats(total_results=0, total_size_bytes=0)

        except Exception as e:
            self.logger.error(f"Failed to get storage stats: {e}")
            return StorageStats(total_results=0, total_size_bytes=0)

    def cleanup_expired_results(self) -> int:
        """Clean up expired results based on TTL."""
        try:
            # ClickHouse TTL should handle this automatically, but we can force cleanup
            cleanup_query = """
            OPTIMIZE TABLE results_metadata FINAL
            """
            self.connection.execute_command(cleanup_query)

            # Also cleanup related tables
            for table in ['results_axes', 'results_facts']:
                self.connection.execute_command(f"OPTIMIZE TABLE {table} FINAL")

            return 0  # ClickHouse doesn't return count for TTL cleanup

        except Exception as e:
            self.logger.error(f"Failed to cleanup expired results: {e}")
            return 0

    def delete_result(self, result_id: str) -> bool:
        """Delete a specific result and all its data."""
        try:
            # Delete from all tables
            tables = ['results_metadata', 'results_axes', 'results_facts']

            for table in tables:
                delete_query = f"""
                ALTER TABLE {table} DELETE WHERE result_id = %(result_id)s
                """
                self.connection.execute_command(delete_query, {'result_id': result_id})

            self.logger.info(f"Successfully deleted result {result_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete result {result_id}: {e}")
            return False
