"""
ClickHouse result storage system for KPI query results.

This module provides a comprehensive system for storing KPI query results in ClickHouse
with support for multiple storage approaches and optimized Parquet export capabilities.
"""

import json
import logging
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

from src.core.connection import ClickHouseConnection
from src.models.result_models import (
    ResultMetadata, StorageApproach, StorageStats, ResultSummary
)
from src.models.axis import Period
from src.models.kpi import KPIType


@dataclass
class StorageConfig:
    """Configuration for result storage."""
    approach: StorageApproach = StorageApproach.COLUMNAR
    default_retention_days: int = 30
    enable_compression: bool = True
    batch_size: int = 10000
    enable_ttl: bool = True


class ResultStore:
    """
    Main class for storing and retrieving KPI results in ClickHouse.
    
    Supports three different storage approaches:
    1. Normalized: Separate tables for metadata, axes, and facts
    2. Wide Table: Single table with fixed columns for axes
    3. Columnar: Array-based storage optimized for Parquet export
    """
    
    def __init__(
        self, 
        connection: ClickHouseConnection,
        config: Optional[StorageConfig] = None
    ):
        """
        Initialize the ResultStore.
        
        Args:
            connection: ClickHouse database connection
            config: Storage configuration options
        """
        self.connection = connection
        self.config = config or StorageConfig()
        self.logger = logging.getLogger(__name__)
        
        # Initialize database tables
        self._initialize_tables()
    
    def _initialize_tables(self) -> None:
        """Initialize ClickHouse tables for result storage."""
        try:
            # Read and execute schema from SQL file
            schema_path = "sql/clickhouse_schemas.sql"
            try:
                with open(schema_path, 'r') as f:
                    schema_sql = f.read()
                
                # Split by semicolon and execute each statement
                statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
                for statement in statements:
                    if statement and not statement.startswith('--'):
                        self.connection.execute_command(statement)
                        
                self.logger.info("Successfully initialized ClickHouse tables")
                
            except FileNotFoundError:
                self.logger.warning(f"Schema file {schema_path} not found, creating basic tables")
                self._create_basic_tables()
                
        except Exception as e:
            self.logger.error(f"Failed to initialize tables: {e}")
            raise
    
    def _create_basic_tables(self) -> None:
        """Create basic tables if schema file is not available."""
        # Create basic normalized tables
        metadata_sql = """
        CREATE TABLE IF NOT EXISTS results_metadata (
            job_id String,
            result_id String,
            analysis_name String,
            id_panel String,
            query_steps String,
            query_ids Array(String),
            username String,
            job_duration String,
            axes_info String,
            filters_info String,
            periods Array(String),
            facts_info String,
            retention_days Int32,
            created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (job_id, result_id, created_at)
        """
        
        axes_sql = """
        CREATE TABLE IF NOT EXISTS results_axes (
            result_id String,
            period_name String,
            axis_name String,
            position_name String,
            position_number UInt32,
            axis_id String,
            created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (result_id, period_name, axis_name, position_number)
        """
        
        facts_sql = """
        CREATE TABLE IF NOT EXISTS results_facts (
            result_id String,
            period_name String,
            fact_name String,
            fact_id String,
            value Float64,
            decimal_places UInt8,
            axis_keys String,
            created_at DateTime DEFAULT now()
        ) ENGINE = MergeTree()
        ORDER BY (result_id, period_name, fact_name, axis_keys)
        """
        
        for sql in [metadata_sql, axes_sql, facts_sql]:
            self.connection.execute_command(sql)
    
    def store_result_direct(
        self,
        query_text: str,
        job_id: str,
        analysis_name: str,
        period: Union[Period, List[Period]],
        kpi_type: KPIType,
        id_panel: int,
        axes: Dict[str, Any],
        filters: Dict[str, Any],
        combined_result_id: Optional[str] = None,
        query_steps: str = "",
        query_ids: Optional[List[str]] = None,
        settings: Optional[Dict[str, Any]] = None,
        username: str = "",
        su_fact_name: Optional[str] = None,
        job_duration: float = 0,
        error_info: Optional[Dict[str, Any]] = None,
        retention_days: int = 30
    ) -> str:
        """
        Store KPI query results directly in ClickHouse using native INSERT INTO ... SELECT.

        This method eliminates intermediate data transfer by executing the KPI query
        directly as INSERT statements into the result tables, leveraging ClickHouse's
        native query processing capabilities for optimal performance.

        Args:
            query_text: The KPI query that was executed
            job_id: Unique job identifier
            analysis_name: Name of the analysis
            period: Period(s) for the analysis
            kpi_type: Type of KPI analysis
            id_panel: Panel identifier
            axes: Axes configuration
            filters: Filters configuration
            combined_result_id: Optional combined result ID
            query_steps: Complete query steps
            query_ids: List of query IDs
            settings: Query execution settings
            username: User who executed the job
            su_fact_name: SU fact name if applicable
            job_duration: Job duration in milliseconds
            error_info: Error information if job failed
            retention_days: Number of days to retain results

        Returns:
            Result ID for the stored data
        """
        try:
            # Generate result ID if not provided
            result_id = combined_result_id or self._generate_result_id(job_id, analysis_name)

            # Convert period to list if single period
            periods = period if isinstance(period, list) else [period]

            # Store metadata first
            self._store_metadata_direct(
                result_id=result_id,
                job_id=job_id,
                analysis_name=analysis_name,
                periods=periods,
                axes=axes,
                filters=filters,
                query_steps=query_steps,
                query_ids=query_ids or [],
                username=username,
                job_duration=job_duration,
                su_fact_name=su_fact_name,
                retention_days=retention_days,
                error_info=error_info
            )

            # Store query results directly if no error
            if not error_info and not query_text.strip().startswith("SELECT 'Error'"):
                self._store_query_results_direct(
                    result_id=result_id,
                    query_text=query_text,
                    periods=periods,
                    axes=axes,
                    settings=settings
                )

            self.logger.info(f"Successfully stored result {result_id} using direct ClickHouse operations")
            return result_id

        except Exception as e:
            self.logger.error(f"Failed to store result: {e}")
            # Store error information if metadata was created
            try:
                self._store_error_direct(result_id, str(e))
            except:
                pass  # Don't fail on error storage failure
            raise
    
    def _generate_result_id(self, job_id: str, analysis_name: str) -> str:
        """Generate a unique result ID."""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        hash_input = f"{job_id}_{analysis_name}_{timestamp}"
        hash_part = hashlib.md5(hash_input.encode()).hexdigest()[:8]
        return f"result_{job_id}_{hash_part}"
    
    def _store_metadata_direct(
        self,
        result_id: str,
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        axes: Dict[str, Any],
        filters: Dict[str, Any],
        query_steps: str,
        query_ids: List[str],
        username: str,
        job_duration: float,
        su_fact_name: Optional[str],
        retention_days: int,
        error_info: Optional[Dict[str, Any]]
    ) -> None:
        """Store metadata directly using INSERT statement."""
        try:
            # Prepare metadata values
            metadata_values = {
                'job_id': job_id,
                'result_id': result_id,
                'analysis_name': analysis_name,
                'id_panel': '1',  # Default panel ID
                'query_steps': query_steps,
                'query_ids': query_ids,
                'username': username,
                'job_duration': self._format_duration(job_duration),
                'axes_info': json.dumps(axes),
                'filters_info': json.dumps(filters),
                'periods': [p.label for p in periods],
                'facts_info': json.dumps({
                    "su_fact_name": su_fact_name,
                    "error_info": error_info
                }) if su_fact_name or error_info else "{}",
                'retention_days': retention_days,
                'created_at': datetime.now()
            }

            # Insert metadata using client.insert for better performance
            self.connection.client.insert('results_metadata', [tuple(metadata_values.values())])

            self.logger.debug(f"Stored metadata for result {result_id}")

        except Exception as e:
            self.logger.error(f"Failed to store metadata: {e}")
            raise

    def _store_query_results_direct(
        self,
        result_id: str,
        query_text: str,
        periods: List[Period],
        axes: Dict[str, Any],
        settings: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Store query results directly using INSERT INTO ... SELECT statements.

        This method analyzes the KPI query structure and creates direct INSERT
        statements to populate the axes and facts tables without intermediate
        data transfer.
        """
        try:
            if self.config.approach == StorageApproach.NORMALIZED:
                self._store_normalized_direct(result_id, query_text, periods, axes, settings)
            elif self.config.approach == StorageApproach.WIDE_TABLE:
                self._store_wide_table_direct(result_id, query_text, periods, axes, settings)
            elif self.config.approach == StorageApproach.COLUMNAR:
                self._store_columnar_direct(result_id, query_text, periods, axes, settings)

        except Exception as e:
            self.logger.error(f"Failed to store query results directly: {e}")
            raise

    def _store_normalized_direct(
        self,
        result_id: str,
        query_text: str,
        periods: List[Period],
        axes: Dict[str, Any],
        settings: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Store results using normalized approach with direct INSERT INTO ... SELECT.

        This method analyzes the KPI query structure and creates optimized INSERT
        statements to populate axes and facts tables directly from the query results.
        """
        try:
            # Create a temporary view of the query results for processing
            temp_view_name = f"temp_kpi_results_{result_id.replace('-', '_')}"

            # Create temporary view with the KPI query
            create_view_sql = f"""
            CREATE OR REPLACE VIEW {temp_view_name} AS
            {query_text}
            """

            self.connection.execute_command(create_view_sql, settings)

            try:
                # Extract and store axes data directly
                self._insert_axes_direct(result_id, temp_view_name, periods, axes)

                # Extract and store facts data directly
                self._insert_facts_direct(result_id, temp_view_name, periods, axes)

            finally:
                # Clean up temporary view
                try:
                    self.connection.execute_command(f"DROP VIEW IF EXISTS {temp_view_name}")
                except Exception as cleanup_error:
                    self.logger.warning(f"Failed to cleanup temporary view {temp_view_name}: {cleanup_error}")

            self.logger.debug(f"Successfully stored normalized results for {result_id}")

        except Exception as e:
            self.logger.error(f"Failed to store normalized results directly: {e}")
            raise

    def _insert_axes_direct(
        self,
        result_id: str,
        temp_view_name: str,
        periods: List[Period],
        axes: Dict[str, Any]
    ) -> None:
        """Insert axes data directly from query results."""
        try:
            # Build INSERT statement for axes data
            # This extracts dimension information from the query results
            axes_insert_sql = f"""
            INSERT INTO results_axes (
                result_id, period_name, axis_name, position_name,
                position_number, axis_id, created_at
            )
            SELECT DISTINCT
                '{result_id}' as result_id,
                COALESCE(period_name, '{periods[0].label}') as period_name,
                axis_name,
                position_name,
                position_number,
                concat(axis_name, '_', toString(position_number)) as axis_id,
                now() as created_at
            FROM (
                {self._build_axes_extraction_query(temp_view_name, axes)}
            )
            """

            self.connection.execute_command(axes_insert_sql)
            self.logger.debug(f"Inserted axes data for result {result_id}")

        except Exception as e:
            self.logger.error(f"Failed to insert axes data directly: {e}")
            raise

    def _insert_facts_direct(
        self,
        result_id: str,
        temp_view_name: str,
        periods: List[Period],
        axes: Dict[str, Any]
    ) -> None:
        """Insert facts data directly from query results."""
        try:
            # Build INSERT statement for facts data
            facts_insert_sql = f"""
            INSERT INTO results_facts (
                result_id, period_name, fact_name, fact_id,
                value, decimal_places, axis_keys, created_at
            )
            SELECT
                '{result_id}' as result_id,
                COALESCE(period_name, '{periods[0].label}') as period_name,
                fact_name,
                concat('fact_', fact_name) as fact_id,
                fact_value,
                {self._get_decimal_places_expression('fact_value')} as decimal_places,
                axis_keys,
                now() as created_at
            FROM (
                {self._build_facts_extraction_query(temp_view_name, axes)}
            )
            WHERE fact_value IS NOT NULL
            """

            self.connection.execute_command(facts_insert_sql)
            self.logger.debug(f"Inserted facts data for result {result_id}")

        except Exception as e:
            self.logger.error(f"Failed to insert facts data directly: {e}")
            raise

    def _build_axes_extraction_query(self, temp_view_name: str, axes: Dict[str, Any]) -> str:
        """
        Build query to extract axes data from KPI results.

        This method analyzes the structure of the KPI query results and creates
        a query to extract dimension information in the format needed for the
        axes table.
        """
        # Build UNION ALL query for each axis found in the results
        axis_queries = []

        # Standard pattern: look for columns ending with _position_number and corresponding _name columns
        axis_extraction_template = """
        SELECT
            '{axis_name}' as axis_name,
            COALESCE({name_column}, toString({position_column})) as position_name,
            {position_column} as position_number,
            period_name
        FROM {temp_view_name}
        WHERE {position_column} IS NOT NULL AND {position_column} > 0
        """

        # Try to detect axes from the query structure
        # This is a heuristic approach based on common KPI query patterns
        common_axes = [
            ('product', 'product_position_number', 'product_name'),
            ('region', 'region_position_number', 'region_name'),
            ('channel', 'channel_position_number', 'channel_name'),
            ('brand', 'brand_position_number', 'brand_name'),
            ('category', 'category_position_number', 'category_name'),
            ('time', 'time_position_number', 'time_name'),
            ('customer', 'customer_position_number', 'customer_name')
        ]

        # Add axes based on configuration
        for axis_name, axis_config in axes.items():
            if isinstance(axis_config, dict) and axis_config.get('name'):
                position_col = f"{axis_config['name']}_position_number"
                name_col = f"{axis_config['name']}_name"

                axis_query = axis_extraction_template.format(
                    axis_name=axis_name,
                    name_column=name_col,
                    position_column=position_col,
                    temp_view_name=temp_view_name
                )
                axis_queries.append(axis_query)

        # If no axes configured, try common patterns
        if not axis_queries:
            for axis_name, position_col, name_col in common_axes:
                axis_query = f"""
                SELECT
                    '{axis_name}' as axis_name,
                    COALESCE({name_col}, toString({position_col})) as position_name,
                    {position_col} as position_number,
                    COALESCE(period_name, '') as period_name
                FROM {temp_view_name}
                WHERE hasColumn('{position_col}') AND {position_col} IS NOT NULL AND {position_col} > 0
                """
                axis_queries.append(axis_query)

        if not axis_queries:
            # Fallback: create a dummy axis entry
            return f"""
            SELECT
                'default' as axis_name,
                'All' as position_name,
                1 as position_number,
                COALESCE(period_name, '') as period_name
            FROM {temp_view_name}
            LIMIT 1
            """

        return " UNION ALL ".join(axis_queries)

    def _build_facts_extraction_query(self, temp_view_name: str, axes: Dict[str, Any]) -> str:
        """
        Build query to extract facts data from KPI results.

        This method creates a query to extract measure/fact information from the
        KPI query results and transform it into the format needed for the facts table.
        """
        # Build query to unpivot fact columns into rows
        # This assumes the KPI query follows the standard pattern with Fact and Value columns

        # Check if query follows the standard KPI pattern (Fact column + Value column)
        standard_facts_query = f"""
        SELECT
            Fact as fact_name,
            Value as fact_value,
            {self._build_axis_keys_expression(axes)} as axis_keys,
            COALESCE(period_name, '') as period_name
        FROM {temp_view_name}
        WHERE Fact IS NOT NULL AND Value IS NOT NULL
        """

        # Alternative: if no Fact column, try to detect numeric columns as facts
        alternative_facts_query = f"""
        SELECT
            fact_name,
            fact_value,
            {self._build_axis_keys_expression(axes)} as axis_keys,
            COALESCE(period_name, '') as period_name
        FROM (
            {self._build_numeric_columns_unpivot(temp_view_name)}
        )
        WHERE fact_value IS NOT NULL
        """

        # Try standard pattern first, fall back to alternative
        return f"""
        {standard_facts_query}
        UNION ALL
        {alternative_facts_query}
        """

    def _build_axis_keys_expression(self, axes: Dict[str, Any]) -> str:
        """Build expression to create axis_keys JSON from position numbers."""
        if not axes:
            return "'{}'"

        # Build JSON object with axis position numbers
        key_expressions = []
        for axis_name, axis_config in axes.items():
            if isinstance(axis_config, dict) and axis_config.get('name'):
                position_col = f"{axis_config['name']}_position_number"
                key_expressions.append(f"'{axis_name}', toString(COALESCE({position_col}, 0))")

        if not key_expressions:
            return "'{}'"

        return f"toJSONString(map({', '.join(key_expressions)}))"

    def _build_numeric_columns_unpivot(self, temp_view_name: str) -> str:
        """Build query to unpivot numeric columns as facts."""
        # This is a simplified approach - in practice, you might want to
        # dynamically detect numeric columns from the table structure
        return f"""
        SELECT 'revenue' as fact_name, revenue as fact_value FROM {temp_view_name} WHERE revenue IS NOT NULL
        UNION ALL
        SELECT 'units' as fact_name, units as fact_value FROM {temp_view_name} WHERE units IS NOT NULL
        UNION ALL
        SELECT 'volume' as fact_name, volume as fact_value FROM {temp_view_name} WHERE volume IS NOT NULL
        UNION ALL
        SELECT 'value' as fact_name, value as fact_value FROM {temp_view_name} WHERE value IS NOT NULL
        """

    def _get_decimal_places_expression(self, column_name: str) -> str:
        """Get ClickHouse expression to calculate decimal places for a numeric column."""
        return f"""
        CASE
            WHEN {column_name} = floor({column_name}) THEN 0
            ELSE length(splitByChar('.', toString({column_name}))[2])
        END
        """

    def _store_wide_table_direct(
        self,
        result_id: str,
        query_text: str,
        periods: List[Period],
        axes: Dict[str, Any],
        settings: Optional[Dict[str, Any]] = None
    ) -> None:
        """Store results using wide table approach with direct INSERT."""
        try:
            # Create temporary view
            temp_view_name = f"temp_kpi_results_{result_id.replace('-', '_')}"
            create_view_sql = f"CREATE OR REPLACE VIEW {temp_view_name} AS {query_text}"
            self.connection.execute_command(create_view_sql, settings)

            try:
                # Insert into wide table with fixed axis columns
                wide_insert_sql = f"""
                INSERT INTO results_wide (
                    result_id, job_id, period_name, analysis_name,
                    axis1_name, axis1_position_name, axis1_position_number,
                    axis2_name, axis2_position_name, axis2_position_number,
                    axis3_name, axis3_position_name, axis3_position_number,
                    axis4_name, axis4_position_name, axis4_position_number,
                    axis5_name, axis5_position_name, axis5_position_number,
                    fact_name, fact_value, fact_decimal_places,
                    username, created_at
                )
                SELECT
                    '{result_id}' as result_id,
                    'job_id' as job_id,  -- Would need to be passed in
                    COALESCE(period_name, '{periods[0].label}') as period_name,
                    'analysis_name' as analysis_name,  -- Would need to be passed in
                    {self._build_wide_table_axis_columns(axes)},
                    fact_name,
                    fact_value,
                    {self._get_decimal_places_expression('fact_value')} as fact_decimal_places,
                    'username' as username,  -- Would need to be passed in
                    now() as created_at
                FROM (
                    {self._build_facts_extraction_query(temp_view_name, axes)}
                )
                """

                self.connection.execute_command(wide_insert_sql)

            finally:
                self.connection.execute_command(f"DROP VIEW IF EXISTS {temp_view_name}")

        except Exception as e:
            self.logger.error(f"Failed to store wide table results directly: {e}")
            raise

    def _store_columnar_direct(
        self,
        result_id: str,
        query_text: str,
        periods: List[Period],
        axes: Dict[str, Any],
        settings: Optional[Dict[str, Any]] = None
    ) -> None:
        """Store results using columnar approach with direct INSERT."""
        try:
            # Create temporary view
            temp_view_name = f"temp_kpi_results_{result_id.replace('-', '_')}"
            create_view_sql = f"CREATE OR REPLACE VIEW {temp_view_name} AS {query_text}"
            self.connection.execute_command(create_view_sql, settings)

            try:
                # Insert into columnar table with arrays
                columnar_insert_sql = f"""
                INSERT INTO results_columnar (
                    result_id, period_name,
                    axis_names, axis_position_names, axis_position_numbers, axis_ids,
                    fact_names, fact_values, fact_decimal_places,
                    row_hash, created_at
                )
                SELECT
                    '{result_id}' as result_id,
                    period_name,
                    groupArray(axis_name) as axis_names,
                    groupArray(position_name) as axis_position_names,
                    groupArray(position_number) as axis_position_numbers,
                    groupArray(axis_id) as axis_ids,
                    groupArray(fact_name) as fact_names,
                    groupArray(fact_value) as fact_values,
                    groupArray({self._get_decimal_places_expression('fact_value')}) as fact_decimal_places,
                    cityHash64(toString(groupArray((axis_name, position_number, fact_name)))) as row_hash,
                    now() as created_at
                FROM (
                    SELECT
                        a.period_name,
                        a.axis_name,
                        a.position_name,
                        a.position_number,
                        concat(a.axis_name, '_', toString(a.position_number)) as axis_id,
                        f.fact_name,
                        f.fact_value
                    FROM (
                        {self._build_axes_extraction_query(temp_view_name, axes)}
                    ) a
                    CROSS JOIN (
                        {self._build_facts_extraction_query(temp_view_name, axes)}
                    ) f
                    WHERE a.period_name = f.period_name
                )
                GROUP BY period_name
                """

                self.connection.execute_command(columnar_insert_sql)

            finally:
                self.connection.execute_command(f"DROP VIEW IF EXISTS {temp_view_name}")

        except Exception as e:
            self.logger.error(f"Failed to store columnar results directly: {e}")
            raise

    def _build_wide_table_axis_columns(self, axes: Dict[str, Any]) -> str:
        """Build column expressions for wide table axis columns."""
        # Initialize all 5 axis columns as empty
        axis_columns = []

        axis_list = list(axes.keys())[:5]  # Limit to 5 axes

        for i in range(5):
            if i < len(axis_list):
                axis_name = axis_list[i]
                axis_config = axes[axis_name]
                if isinstance(axis_config, dict) and axis_config.get('name'):
                    name_col = f"{axis_config['name']}_name"
                    position_col = f"{axis_config['name']}_position_number"

                    axis_columns.extend([
                        f"'{axis_name}' as axis{i+1}_name",
                        f"COALESCE({name_col}, '') as axis{i+1}_position_name",
                        f"COALESCE({position_col}, 0) as axis{i+1}_position_number"
                    ])
                else:
                    axis_columns.extend([
                        f"'' as axis{i+1}_name",
                        f"'' as axis{i+1}_position_name",
                        f"0 as axis{i+1}_position_number"
                    ])
            else:
                axis_columns.extend([
                    f"'' as axis{i+1}_name",
                    f"'' as axis{i+1}_position_name",
                    f"0 as axis{i+1}_position_number"
                ])

        return ",\n                    ".join(axis_columns)

    def _store_error_direct(self, result_id: str, error_message: str) -> None:
        """Store error information directly."""
        try:
            error_insert_sql = f"""
            INSERT INTO results_facts (
                result_id, period_name, fact_name, fact_id,
                value, decimal_places, axis_keys, created_at
            )
            VALUES (
                '{result_id}', 'error', 'error_message', 'error',
                0.0, 0, '{{"error": "{error_message}"}}', now()
            )
            """

            self.connection.execute_command(error_insert_sql)

        except Exception as e:
            self.logger.warning(f"Failed to store error information: {e}")

    # =============================================================================
    # UTILITY METHODS
    # =============================================================================

    def _format_duration(self, duration_ms: float) -> str:
        """Format duration in milliseconds to human-readable string."""
        if duration_ms < 1000:
            return f"{duration_ms:.0f}ms"
        elif duration_ms < 60000:
            return f"{duration_ms/1000:.1f}s"
        else:
            minutes = int(duration_ms / 60000)
            seconds = (duration_ms % 60000) / 1000
            return f"{minutes}m {seconds:.1f}s"

    # Legacy method - kept for compatibility but not used in direct storage
    def _insert_metadata(self, metadata: ResultMetadata) -> None:
        """Insert metadata into ClickHouse (legacy method)."""
        values = (
            metadata.job_id,
            metadata.result_id,
            metadata.analysis_name,
            metadata.id_panel,
            metadata.query_steps,
            metadata.query_ids,
            metadata.username,
            metadata.job_duration,
            metadata.axes_info,
            metadata.filters_info,
            metadata.periods,
            metadata.facts_info,
            metadata.retention_days,
            datetime.now()
        )

        self.connection.client.insert('results_metadata', [values])

    # Legacy methods - removed in favor of direct storage approach

    # All legacy DataFrame-based methods have been removed in favor of direct ClickHouse operations

    # =============================================================================
    # COMPATIBILITY METHODS (for existing codebase)
    # =============================================================================

    def create_result_table_for_all_periods(self, result_id: str) -> Dict[str, Any]:
        """
        Compatibility method for existing codebase.

        Returns information about the stored result.
        """
        try:
            summary = self.get_result_summary(result_id)
            return {
                'created': True,
                'row_count': summary.total_rows if summary else 0,
                'result_id': result_id
            }
        except Exception as e:
            self.logger.error(f"Failed to get result summary for {result_id}: {e}")
            return {
                'created': False,
                'row_count': 0,
                'error': str(e)
            }

    def get_result_summary(self, result_id: str) -> Optional[ResultSummary]:
        """Get summary information about a stored result."""
        try:
            # Query metadata
            metadata_query = """
            SELECT * FROM results_metadata
            WHERE result_id = %(result_id)s
            LIMIT 1
            """

            metadata_result = self.connection.get_query_result(
                metadata_query,
                settings={'result_id': result_id}
            )

            if not metadata_result or len(metadata_result.data) == 0:
                return None

            metadata = metadata_result.data[0]

            # Count rows in axes and facts tables
            axes_count_query = """
            SELECT count() as cnt FROM results_axes
            WHERE result_id = %(result_id)s
            """

            facts_count_query = """
            SELECT count() as cnt FROM results_facts
            WHERE result_id = %(result_id)s
            """

            axes_count = self.connection.get_query_result(
                axes_count_query,
                settings={'result_id': result_id}
            ).data[0]['cnt']

            facts_count = self.connection.get_query_result(
                facts_count_query,
                settings={'result_id': result_id}
            ).data[0]['cnt']

            return ResultSummary(
                result_id=result_id,
                job_id=metadata['job_id'],
                analysis_name=metadata['analysis_name'],
                periods=metadata['periods'],
                total_rows=axes_count + facts_count,
                axes_count=axes_count,
                facts_count=facts_count,
                storage_approach=self.config.approach,
                created_at=metadata['created_at'],
                file_size_estimate_mb=self._estimate_file_size(axes_count + facts_count)
            )

        except Exception as e:
            self.logger.error(f"Failed to get result summary: {e}")
            return None

    def _estimate_file_size(self, row_count: int) -> float:
        """Estimate file size in MB based on row count."""
        # Rough estimate: 100 bytes per row on average
        return (row_count * 100) / (1024 * 1024)

    def get_storage_stats(self) -> StorageStats:
        """Get statistics about stored results."""
        try:
            stats_query = """
            SELECT
                count() as total_results,
                min(created_at) as oldest_result,
                max(created_at) as newest_result
            FROM results_metadata
            """

            result = self.connection.get_query_result(stats_query)
            if result and result.data:
                data = result.data[0]
                return StorageStats(
                    total_results=data['total_results'],
                    total_size_bytes=0,  # Would need more complex calculation
                    oldest_result=data['oldest_result'],
                    newest_result=data['newest_result'],
                    results_by_approach={self.config.approach: data['total_results']}
                )

            return StorageStats(total_results=0, total_size_bytes=0)

        except Exception as e:
            self.logger.error(f"Failed to get storage stats: {e}")
            return StorageStats(total_results=0, total_size_bytes=0)

    def cleanup_expired_results(self) -> int:
        """Clean up expired results based on TTL."""
        try:
            # ClickHouse TTL should handle this automatically, but we can force cleanup
            cleanup_query = """
            OPTIMIZE TABLE results_metadata FINAL
            """
            self.connection.execute_command(cleanup_query)

            # Also cleanup related tables
            for table in ['results_axes', 'results_facts']:
                self.connection.execute_command(f"OPTIMIZE TABLE {table} FINAL")

            return 0  # ClickHouse doesn't return count for TTL cleanup

        except Exception as e:
            self.logger.error(f"Failed to cleanup expired results: {e}")
            return 0

    def delete_result(self, result_id: str) -> bool:
        """Delete a specific result and all its data."""
        try:
            # Delete from all tables
            tables = ['results_metadata', 'results_axes', 'results_facts']

            for table in tables:
                delete_query = f"""
                ALTER TABLE {table} DELETE WHERE result_id = %(result_id)s
                """
                self.connection.execute_command(delete_query, {'result_id': result_id})

            self.logger.info(f"Successfully deleted result {result_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to delete result {result_id}: {e}")
            return False
