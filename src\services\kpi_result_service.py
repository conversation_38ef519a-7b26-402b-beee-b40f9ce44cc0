"""
KPI Result Service for managing stored KPI query results.

This service provides high-level operations for managing KPI results stored in ClickHouse,
including querying, filtering, and preparing data for export.
"""

import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

from src.core.connection import ClickHouseConnection
from src.core.result_store import ResultStore, StorageConfig
from src.models.result_models import (
    ResultSummary, StorageStats, ExportRequest, ExportResult,
    StorageApproach, ExportFormat
)
from src.services.base_service import BaseService


class KPIResultService(BaseService):
    """
    Service for managing KPI results with high-level operations.
    
    This service provides a clean interface for:
    - Querying stored results
    - Filtering and searching results
    - Preparing data for export
    - Managing result lifecycle
    """
    
    def __init__(
        self,
        connection: Optional[ClickHouseConnection] = None,
        storage_config: Optional[StorageConfig] = None,
        msg_logger_func: Optional[callable] = None
    ):
        """
        Initialize the KPI Result Service.
        
        Args:
            connection: ClickHouse connection
            storage_config: Storage configuration
            msg_logger_func: Optional message logger function
        """
        super().__init__(msg_logger_func)
        
        from src.core.connection_manager import connection_manager
        self.connection = connection or connection_manager.get_clickhouse_connection()
        
        self.storage_config = storage_config or StorageConfig()
        self.result_store = ResultStore(self.connection, self.storage_config)
        
        self.logger = logging.getLogger(__name__)
    
    def list_results(
        self,
        job_id: Optional[str] = None,
        username: Optional[str] = None,
        analysis_name: Optional[str] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        limit: int = 100
    ) -> List[ResultSummary]:
        """
        List stored KPI results with optional filtering.
        
        Args:
            job_id: Filter by job ID
            username: Filter by username
            analysis_name: Filter by analysis name
            date_from: Filter results created after this date
            date_to: Filter results created before this date
            limit: Maximum number of results to return
            
        Returns:
            List of result summaries
        """
        try:
            # Build WHERE clause
            where_conditions = []
            params = {}
            
            if job_id:
                where_conditions.append("job_id = %(job_id)s")
                params['job_id'] = job_id
            
            if username:
                where_conditions.append("username = %(username)s")
                params['username'] = username
            
            if analysis_name:
                where_conditions.append("analysis_name LIKE %(analysis_name)s")
                params['analysis_name'] = f"%{analysis_name}%"
            
            if date_from:
                where_conditions.append("created_at >= %(date_from)s")
                params['date_from'] = date_from
            
            if date_to:
                where_conditions.append("created_at <= %(date_to)s")
                params['date_to'] = date_to
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            
            # Query metadata
            query = f"""
            SELECT 
                result_id,
                job_id,
                analysis_name,
                periods,
                created_at,
                retention_days,
                username
            FROM results_metadata
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %(limit)s
            """
            
            params['limit'] = limit
            
            result = self.connection.get_query_result(query, settings=params)
            
            if not result or not result.data:
                return []
            
            # Convert to ResultSummary objects
            summaries = []
            for row in result.data:
                # Get additional stats for each result
                stats = self._get_result_stats(row['result_id'])
                
                summary = ResultSummary(
                    result_id=row['result_id'],
                    job_id=row['job_id'],
                    analysis_name=row['analysis_name'],
                    periods=row['periods'],
                    total_rows=stats['total_rows'],
                    axes_count=stats['axes_count'],
                    facts_count=stats['facts_count'],
                    storage_approach=self.storage_config.approach,
                    created_at=row['created_at'],
                    expires_at=row['created_at'] + timedelta(days=row['retention_days']),
                    file_size_estimate_mb=self._estimate_file_size(stats['total_rows'])
                )
                summaries.append(summary)
            
            return summaries
            
        except Exception as e:
            self.logger.error(f"Failed to list results: {e}")
            return []
    
    def get_result_details(self, result_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a specific result.
        
        Args:
            result_id: Result identifier
            
        Returns:
            Detailed result information or None if not found
        """
        try:
            # Get metadata
            metadata_query = """
            SELECT * FROM results_metadata 
            WHERE result_id = %(result_id)s
            LIMIT 1
            """
            
            metadata_result = self.connection.get_query_result(
                metadata_query, 
                settings={'result_id': result_id}
            )
            
            if not metadata_result or not metadata_result.data:
                return None
            
            metadata = metadata_result.data[0]
            
            # Get axes information
            axes_query = """
            SELECT 
                axis_name,
                count() as position_count,
                min(position_number) as min_position,
                max(position_number) as max_position
            FROM results_axes 
            WHERE result_id = %(result_id)s
            GROUP BY axis_name
            ORDER BY axis_name
            """
            
            axes_result = self.connection.get_query_result(
                axes_query,
                settings={'result_id': result_id}
            )
            
            # Get facts information
            facts_query = """
            SELECT 
                fact_name,
                count() as value_count,
                min(value) as min_value,
                max(value) as max_value,
                avg(value) as avg_value
            FROM results_facts 
            WHERE result_id = %(result_id)s
            GROUP BY fact_name
            ORDER BY fact_name
            """
            
            facts_result = self.connection.get_query_result(
                facts_query,
                settings={'result_id': result_id}
            )
            
            return {
                'metadata': metadata,
                'axes': axes_result.data if axes_result else [],
                'facts': facts_result.data if facts_result else [],
                'storage_approach': self.storage_config.approach.value
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get result details for {result_id}: {e}")
            return None
    
    def _get_result_stats(self, result_id: str) -> Dict[str, int]:
        """Get basic statistics for a result."""
        try:
            # Count axes and facts
            axes_query = "SELECT count() as cnt FROM results_axes WHERE result_id = %(result_id)s"
            facts_query = "SELECT count() as cnt FROM results_facts WHERE result_id = %(result_id)s"
            
            axes_result = self.connection.get_query_result(axes_query, settings={'result_id': result_id})
            facts_result = self.connection.get_query_result(facts_query, settings={'result_id': result_id})
            
            axes_count = axes_result.data[0]['cnt'] if axes_result and axes_result.data else 0
            facts_count = facts_result.data[0]['cnt'] if facts_result and facts_result.data else 0
            
            return {
                'axes_count': axes_count,
                'facts_count': facts_count,
                'total_rows': axes_count + facts_count
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get result stats for {result_id}: {e}")
            return {'axes_count': 0, 'facts_count': 0, 'total_rows': 0}
    
    def _estimate_file_size(self, row_count: int) -> float:
        """Estimate file size in MB based on row count."""
        # Rough estimate: 100 bytes per row on average
        return (row_count * 100) / (1024 * 1024)
    
    def search_results(self, search_term: str, limit: int = 50) -> List[ResultSummary]:
        """
        Search results by analysis name or job ID.
        
        Args:
            search_term: Term to search for
            limit: Maximum number of results
            
        Returns:
            List of matching result summaries
        """
        return self.list_results(
            analysis_name=search_term,
            limit=limit
        )
    
    def get_storage_statistics(self) -> StorageStats:
        """Get overall storage statistics."""
        return self.result_store.get_storage_stats()
    
    def cleanup_expired_results(self) -> int:
        """Clean up expired results."""
        return self.result_store.cleanup_expired_results()
    
    def delete_result(self, result_id: str) -> bool:
        """Delete a specific result."""
        return self.result_store.delete_result(result_id)
    
    def get_export_preview(
        self, 
        result_id: str, 
        export_format: ExportFormat,
        limit: int = 100
    ) -> Optional[Dict[str, Any]]:
        """
        Get a preview of how the data would look when exported.
        
        Args:
            result_id: Result to preview
            export_format: Export format to preview
            limit: Number of rows to include in preview
            
        Returns:
            Preview data structure
        """
        try:
            if export_format == ExportFormat.PIVOT:
                return self._get_pivot_preview(result_id, limit)
            else:
                return self._get_horizontal_preview(result_id, limit)
                
        except Exception as e:
            self.logger.error(f"Failed to get export preview: {e}")
            return None
    
    def _get_pivot_preview(self, result_id: str, limit: int) -> Dict[str, Any]:
        """Get preview for pivot format."""
        # This would implement pivot format preview logic
        return {
            'format': 'pivot',
            'sample_data': [],
            'column_info': {},
            'row_count_estimate': 0
        }
    
    def _get_horizontal_preview(self, result_id: str, limit: int) -> Dict[str, Any]:
        """Get preview for horizontal facts format."""
        # This would implement horizontal format preview logic
        return {
            'format': 'horizontal_facts',
            'sample_data': [],
            'column_info': {},
            'row_count_estimate': 0
        }
