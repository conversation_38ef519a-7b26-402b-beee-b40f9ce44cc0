"""
Parquet Exporter for KPI Results.

This module provides functionality to export KPI results from ClickHouse to Parquet format
with support for both pivot and horizontal facts formats.
"""

import os
import time
import logging
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

from src.core.connection import ClickHouseConnection
from src.models.result_models import (
    ExportRequest, ExportResult, ExportFormat,
    PivotExportConfig, HorizontalExportConfig
)


class ParquetExporter:
    """
    Exporter for converting ClickHouse KPI results to Parquet format.
    
    Supports two main export formats:
    1. Pivot Format: Axes as columns, facts as rows with values
    2. Horizontal Facts Format: Each fact-axis combination as a separate row
    """
    
    def __init__(self, connection: ClickHouseConnection):
        """
        Initialize the Parquet exporter.
        
        Args:
            connection: ClickHouse database connection
        """
        self.connection = connection
        self.logger = logging.getLogger(__name__)
    
    def export_results(
        self,
        export_request: ExportRequest,
        pivot_config: Optional[PivotExportConfig] = None,
        horizontal_config: Optional[HorizontalExportConfig] = None
    ) -> ExportResult:
        """
        Export KPI results to Parquet format.
        
        Args:
            export_request: Export configuration
            pivot_config: Configuration for pivot format (if applicable)
            horizontal_config: Configuration for horizontal format (if applicable)
            
        Returns:
            Export result with success status and metadata
        """
        start_time = time.time()
        
        try:
            # Validate request
            if not export_request.result_ids:
                return ExportResult(
                    success=False,
                    error_message="No result IDs provided"
                )
            
            # Create output directory if it doesn't exist
            output_path = Path(export_request.output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Export based on format
            if export_request.export_format == ExportFormat.PIVOT:
                result = self._export_pivot_format(
                    export_request, 
                    pivot_config or PivotExportConfig(axis_columns=[], fact_rows=[])
                )
            else:
                result = self._export_horizontal_format(
                    export_request,
                    horizontal_config or HorizontalExportConfig()
                )
            
            # Calculate duration
            duration_ms = (time.time() - start_time) * 1000
            result.export_duration_ms = duration_ms
            
            if result.success:
                # Get file size
                if result.file_path and os.path.exists(result.file_path):
                    result.file_size_bytes = os.path.getsize(result.file_path)
                
                self.logger.info(
                    f"Successfully exported {result.row_count} rows to {result.file_path} "
                    f"({result.file_size_bytes / 1024 / 1024:.2f} MB) in {duration_ms:.2f}ms"
                )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Export failed: {e}")
            return ExportResult(
                success=False,
                error_message=str(e),
                export_duration_ms=(time.time() - start_time) * 1000
            )
    
    def _export_pivot_format(
        self,
        export_request: ExportRequest,
        pivot_config: PivotExportConfig
    ) -> ExportResult:
        """Export results in pivot format."""
        try:
            # Build query for pivot format
            query = self._build_pivot_query(export_request, pivot_config)
            
            # Execute query and get DataFrame
            df = self.connection.get_query_dataframe(query)
            
            if df.empty:
                return ExportResult(
                    success=False,
                    error_message="No data found for the specified result IDs"
                )
            
            # Transform to pivot format
            pivot_df = self._transform_to_pivot(df, pivot_config)
            
            # Write to Parquet
            self._write_parquet(pivot_df, export_request.output_path, export_request.compression)
            
            # Prepare metadata
            metadata = {
                'format': 'pivot',
                'result_ids': export_request.result_ids,
                'axis_columns': pivot_config.axis_columns,
                'fact_rows': pivot_config.fact_rows,
                'export_timestamp': time.time()
            }
            
            return ExportResult(
                success=True,
                file_path=export_request.output_path,
                row_count=len(pivot_df),
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"Pivot export failed: {e}")
            return ExportResult(
                success=False,
                error_message=f"Pivot export failed: {str(e)}"
            )
    
    def _export_horizontal_format(
        self,
        export_request: ExportRequest,
        horizontal_config: HorizontalExportConfig
    ) -> ExportResult:
        """Export results in horizontal facts format."""
        try:
            # Build query for horizontal format
            query = self._build_horizontal_query(export_request, horizontal_config)
            
            # Execute query and get DataFrame
            df = self.connection.get_query_dataframe(query)
            
            if df.empty:
                return ExportResult(
                    success=False,
                    error_message="No data found for the specified result IDs"
                )
            
            # Transform to horizontal format
            horizontal_df = self._transform_to_horizontal(df, horizontal_config)
            
            # Handle separate periods if requested
            if horizontal_config.separate_periods:
                return self._export_separate_periods(
                    horizontal_df, 
                    export_request, 
                    horizontal_config
                )
            else:
                # Write single file
                self._write_parquet(
                    horizontal_df, 
                    export_request.output_path, 
                    export_request.compression
                )
                
                metadata = {
                    'format': 'horizontal_facts',
                    'result_ids': export_request.result_ids,
                    'separate_periods': False,
                    'export_timestamp': time.time()
                }
                
                return ExportResult(
                    success=True,
                    file_path=export_request.output_path,
                    row_count=len(horizontal_df),
                    metadata=metadata
                )
            
        except Exception as e:
            self.logger.error(f"Horizontal export failed: {e}")
            return ExportResult(
                success=False,
                error_message=f"Horizontal export failed: {str(e)}"
            )
    
    def _build_pivot_query(
        self,
        export_request: ExportRequest,
        pivot_config: PivotExportConfig
    ) -> str:
        """Build SQL query for pivot format export."""
        
        # Base query to get axes and facts data
        result_ids_str = "', '".join(export_request.result_ids)
        
        query = f"""
        SELECT 
            rm.result_id,
            rm.job_id,
            rm.analysis_name,
            ra.period_name,
            ra.axis_name,
            ra.position_name,
            ra.position_number,
            rf.fact_name,
            rf.value,
            rf.decimal_places
        FROM results_metadata rm
        LEFT JOIN results_axes ra ON rm.result_id = ra.result_id
        LEFT JOIN results_facts rf ON rm.result_id = rf.result_id AND ra.period_name = rf.period_name
        WHERE rm.result_id IN ('{result_ids_str}')
        """
        
        # Add period filter if specified
        if export_request.periods:
            periods_str = "', '".join(export_request.periods)
            query += f" AND ra.period_name IN ('{periods_str}')"
        
        # Add fact filter if specified
        if export_request.facts:
            facts_str = "', '".join(export_request.facts)
            query += f" AND rf.fact_name IN ('{facts_str}')"
        
        query += " ORDER BY rm.result_id, ra.period_name, ra.axis_name, ra.position_number, rf.fact_name"
        
        return query
    
    def _build_horizontal_query(
        self,
        export_request: ExportRequest,
        horizontal_config: HorizontalExportConfig
    ) -> str:
        """Build SQL query for horizontal format export."""
        
        result_ids_str = "', '".join(export_request.result_ids)
        
        # Select metadata columns if requested
        metadata_columns = ""
        if horizontal_config.include_metadata_columns:
            metadata_columns = """
            rm.job_id,
            rm.analysis_name,
            rm.username,
            rm.created_at,
            """
        
        query = f"""
        SELECT 
            rm.result_id,
            {metadata_columns}
            ra.period_name,
            ra.axis_name,
            ra.position_name,
            ra.position_number,
            rf.{horizontal_config.fact_name_column} as fact_name,
            rf.{horizontal_config.fact_value_column} as fact_value,
            rf.decimal_places
        FROM results_metadata rm
        LEFT JOIN results_axes ra ON rm.result_id = ra.result_id
        LEFT JOIN results_facts rf ON rm.result_id = rf.result_id AND ra.period_name = rf.period_name
        WHERE rm.result_id IN ('{result_ids_str}')
        """
        
        # Add filters
        if export_request.periods:
            periods_str = "', '".join(export_request.periods)
            query += f" AND ra.period_name IN ('{periods_str}')"
        
        if export_request.facts:
            facts_str = "', '".join(export_request.facts)
            query += f" AND rf.fact_name IN ('{facts_str}')"
        
        query += " ORDER BY rm.result_id, ra.period_name, ra.axis_name, ra.position_number, rf.fact_name"
        
        return query
    
    def _transform_to_pivot(
        self,
        df: pd.DataFrame,
        pivot_config: PivotExportConfig
    ) -> pd.DataFrame:
        """Transform DataFrame to pivot format."""
        try:
            # If no specific configuration, create a basic pivot
            if not pivot_config.axis_columns:
                # Use all axis columns as pivot columns
                axis_cols = [col for col in df.columns if 'axis_name' in col or 'position_name' in col]
                pivot_config.axis_columns = axis_cols
            
            # Create pivot table
            pivot_df = df.pivot_table(
                index=['result_id', 'period_name'] + pivot_config.fact_rows,
                columns=pivot_config.axis_columns,
                values=pivot_config.value_column,
                fill_value=pivot_config.null_value
            )
            
            # Reset index to make it a regular DataFrame
            pivot_df = pivot_df.reset_index()
            
            return pivot_df
            
        except Exception as e:
            self.logger.warning(f"Pivot transformation failed, returning original data: {e}")
            return df
    
    def _transform_to_horizontal(
        self,
        df: pd.DataFrame,
        horizontal_config: HorizontalExportConfig
    ) -> pd.DataFrame:
        """Transform DataFrame to horizontal facts format."""
        # The query already returns data in horizontal format
        # Just rename columns if needed
        
        if horizontal_config.fact_name_column != 'fact_name':
            df = df.rename(columns={'fact_name': horizontal_config.fact_name_column})
        
        if horizontal_config.fact_value_column != 'fact_value':
            df = df.rename(columns={'fact_value': horizontal_config.fact_value_column})

        return df

    def _export_separate_periods(
        self,
        df: pd.DataFrame,
        export_request: ExportRequest,
        horizontal_config: HorizontalExportConfig
    ) -> ExportResult:
        """Export separate files for each period."""
        try:
            periods = df['period_name'].unique()
            output_path = Path(export_request.output_path)
            base_name = output_path.stem
            extension = output_path.suffix

            total_rows = 0
            exported_files = []

            for period in periods:
                period_df = df[df['period_name'] == period]
                period_file = output_path.parent / f"{base_name}_{period}{extension}"

                self._write_parquet(period_df, str(period_file), export_request.compression)

                total_rows += len(period_df)
                exported_files.append(str(period_file))

            metadata = {
                'format': 'horizontal_facts',
                'result_ids': export_request.result_ids,
                'separate_periods': True,
                'exported_files': exported_files,
                'periods': list(periods),
                'export_timestamp': time.time()
            }

            return ExportResult(
                success=True,
                file_path=export_request.output_path,  # Main path
                row_count=total_rows,
                metadata=metadata
            )

        except Exception as e:
            self.logger.error(f"Separate periods export failed: {e}")
            return ExportResult(
                success=False,
                error_message=f"Separate periods export failed: {str(e)}"
            )

    def _write_parquet(
        self,
        df: pd.DataFrame,
        file_path: str,
        compression: str = "snappy"
    ) -> None:
        """Write DataFrame to Parquet file."""
        try:
            # Convert DataFrame to Arrow Table for better control
            table = pa.Table.from_pandas(df)

            # Write with specified compression
            pq.write_table(
                table,
                file_path,
                compression=compression,
                use_dictionary=True,  # Use dictionary encoding for string columns
                row_group_size=50000,  # Optimize row group size
                use_deprecated_int96_timestamps=False
            )

            self.logger.debug(f"Successfully wrote {len(df)} rows to {file_path}")

        except Exception as e:
            self.logger.error(f"Failed to write Parquet file {file_path}: {e}")
            raise

    def get_export_schema(self, export_format: ExportFormat) -> Dict[str, str]:
        """
        Get the expected schema for an export format.

        Args:
            export_format: Export format to get schema for

        Returns:
            Dictionary mapping column names to data types
        """
        if export_format == ExportFormat.PIVOT:
            return {
                'result_id': 'string',
                'period_name': 'string',
                'fact_name': 'string',
                # Dynamic axis columns would be added based on data
                'value': 'double'
            }
        else:  # HORIZONTAL_FACTS
            return {
                'result_id': 'string',
                'job_id': 'string',
                'analysis_name': 'string',
                'username': 'string',
                'created_at': 'timestamp',
                'period_name': 'string',
                'axis_name': 'string',
                'position_name': 'string',
                'position_number': 'int32',
                'fact_name': 'string',
                'fact_value': 'double',
                'decimal_places': 'int8'
            }

    def validate_export_request(self, export_request: ExportRequest) -> List[str]:
        """
        Validate an export request and return any validation errors.

        Args:
            export_request: Export request to validate

        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []

        if not export_request.result_ids:
            errors.append("No result IDs provided")

        if not export_request.output_path:
            errors.append("No output path provided")

        # Check if output directory is writable
        try:
            output_path = Path(export_request.output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Try to create a test file
            test_file = output_path.parent / ".write_test"
            test_file.touch()
            test_file.unlink()

        except Exception as e:
            errors.append(f"Output path is not writable: {str(e)}")

        # Validate compression format
        valid_compressions = ['snappy', 'gzip', 'brotli', 'lz4', 'zstd']
        if export_request.compression not in valid_compressions:
            errors.append(f"Invalid compression format. Must be one of: {valid_compressions}")

        return errors
